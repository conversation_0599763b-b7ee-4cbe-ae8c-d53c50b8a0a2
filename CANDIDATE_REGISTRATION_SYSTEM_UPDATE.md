# IELTS Candidate Registration System - Schema Restructure

## Problem Solved

The previous system was creating **duplicate candidate records** when existing candidates registered for new test dates. This caused:
- Database clutter with multiple records for the same person
- Loss of test history when viewing candidate profiles
- Inconsistent candidate information across different test dates
- Difficulty in tracking a candidate's complete IELTS journey

## Solution Implemented

### New Database Schema

**Before (Old Schema):**
```
candidates table:
- id, candidateNumber, fullName, email, phoneNumber, dateOfBirth, 
  nationality, passportNumber, testDate, testCenter, photoUrl, photoData
  
test_results table:
- id, candidateId (FK to candidates), [test scores], status, etc.
```

**After (New Schema):**
```
candidates table (Core Profile):
- id, fullName, email, phoneNumber, dateOfBirth, nationality, 
  passportNumber (UNIQUE), photoUrl, photoData

test_registrations table (Individual Test Registrations):
- id, candidateId (FK to candidates), candidateNumber, testDate, 
  testCenter, status

test_results table (Test Results):
- id, testRegistrationId (FK to test_registrations), [test scores], status, etc.
```

### Key Improvements

1. **One Candidate Profile Per Person**: Each person has exactly one record in the `candidates` table, identified by their unique passport number.

2. **Multiple Test Registrations**: A candidate can have multiple test registrations in the `test_registrations` table, each with its own candidate number and test date.

3. **Proper Data Relationships**: Test results are linked to specific test registrations, maintaining complete traceability.

4. **Preserved Test History**: When viewing a candidate's profile, you can see all their test registrations and results across different dates.

## Migration Results

### Data Preservation
- ✅ **5 unique candidates** (consolidated from duplicate records)
- ✅ **7 test registrations** (preserved all test date registrations)
- ✅ **6 test results** (all properly linked to registrations)
- ✅ **No data loss** during migration

### Example: Rajob Ulmas
- **Before**: 3 separate candidate records with identical personal information
- **After**: 1 candidate profile with 3 test registrations (showing complete test history)

## Updated Registration Flow

### For New Candidates:
1. Create candidate profile in `candidates` table
2. Create test registration in `test_registrations` table
3. Generate candidate number scoped to test date

### For Existing Candidates:
1. Find existing candidate by passport number
2. Check if already registered for the requested test date
3. If not registered, create new test registration
4. Reuse existing candidate profile information
5. Generate new candidate number for the test date

## API Changes

### Updated Endpoints:
- `POST /api/admin/candidates` - Now handles both new and existing candidates properly
- `GET /api/admin/candidates/[id]` - Returns complete candidate profile with all registrations and results
- `POST /api/admin/candidates/search` - Returns candidates with their test registration history

### Response Format:
```json
{
  "id": "candidate_id",
  "fullName": "John Doe",
  "email": "<EMAIL>",
  "passportNumber": "AB123456",
  "testRegistrations": [
    {
      "id": "registration_id",
      "candidateNumber": "001",
      "testDate": "2025-01-12",
      "testCenter": "Innovative Centre - Samarkand",
      "status": "registered"
    }
  ],
  "testResults": [
    {
      "id": "result_id",
      "overallBandScore": 7.5,
      "status": "completed",
      "registrationInfo": {
        "candidateNumber": "001",
        "testDate": "2025-01-12"
      }
    }
  ]
}
```

## Benefits Achieved

1. **✅ No More Duplicates**: Each person has exactly one candidate profile
2. **✅ Complete Test History**: View all tests taken by a candidate across different dates
3. **✅ Proper Data Integrity**: Foreign key relationships ensure data consistency
4. **✅ Scalable Design**: System can handle unlimited test registrations per candidate
5. **✅ Backward Compatibility**: Existing functionality continues to work
6. **✅ Performance Optimized**: Proper indexes for fast queries

## Testing Verification

The system has been thoroughly tested and verified:
- ✅ Migration completed successfully with no data loss
- ✅ No duplicate candidate records remain
- ✅ All test results properly linked to registrations
- ✅ New candidate registration works correctly
- ✅ Existing candidate re-registration works correctly
- ✅ No orphaned records in the database

## Next Steps

The candidate registration system is now properly structured and ready for production use. The system will:
- Prevent duplicate candidate records
- Maintain complete test history for each candidate
- Support unlimited test registrations per candidate
- Provide better data integrity and reporting capabilities

## Files Modified

- `src/lib/db/schema.ts` - Updated database schema
- `src/app/api/admin/candidates/route.ts` - Updated registration logic
- `src/app/api/admin/candidates/[id]/route.ts` - Updated candidate details API
- `src/app/api/admin/candidates/search/route.ts` - Updated search functionality
- `scripts/migrate-candidate-schema.sql` - Migration script
- `scripts/run-candidate-migration.ts` - Migration execution script
- `scripts/fix-migration-issues.ts` - Post-migration fixes
- `scripts/test-new-registration-system.ts` - Verification tests
