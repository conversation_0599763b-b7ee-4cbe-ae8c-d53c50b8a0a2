# Database Setup Guide

## Quick Setup for Development

The IELTS Certification System is currently configured to handle database connection failures gracefully. When the database is not available, the system will:

- Return empty/mock data instead of crashing
- Display appropriate "No data" messages in the UI
- Allow you to explore the interface without a database

## Option 1: Run Without Database (Recommended for Demo)

The system will work without a database connection. You'll see:
- Empty dashboard statistics (showing 0 for all counts)
- Empty results lists
- All UI components and navigation working
- Authentication still functional

## Option 2: Set Up PostgreSQL Database

If you want to use the full functionality with data persistence:

### 1. Install PostgreSQL

**Windows:**
```bash
# Download and install from https://www.postgresql.org/download/windows/
# Or use chocolatey:
choco install postgresql
```

**macOS:**
```bash
# Using Homebrew:
brew install postgresql
brew services start postgresql
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 2. Create Database and User

```bash
# Connect to PostgreSQL as superuser
sudo -u postgres psql

# Create database
CREATE DATABASE ielts_certification;

# Create user with password
CREATE USER ielts_user WITH PASSWORD 'your_secure_password';

# Grant privileges
GRANT ALL PRIVILEGES ON DATABASE ielts_certification TO ielts_user;

# Exit
\q
```

### 3. Update Environment Variables

Edit your `.env.local` file:

```env
# Replace with your actual database credentials
DATABASE_URL="postgresql://ielts_user:your_secure_password@localhost:5432/ielts_certification"

# Keep other variables as they are
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"
ANTHROPIC_API_KEY="your-anthropic-api-key-here"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"
```

### 4. Run Database Migrations

```bash
# Generate migration files (if needed)
npm run db:generate

# Apply migrations to create tables
npm run db:migrate

# Set up initial users and data
npm run db:setup
```

### 5. Verify Setup

After running the setup, you should see:
```
✅ Admin user created
✅ Test checker user created
✅ Database setup completed!

Demo credentials:
Admin: <EMAIL> / admin123
Test Checker: <EMAIL> / checker123
```

## Option 3: Use Docker (Alternative)

If you prefer Docker for PostgreSQL:

```bash
# Run PostgreSQL in Docker
docker run --name ielts-postgres \
  -e POSTGRES_DB=ielts_certification \
  -e POSTGRES_USER=ielts_user \
  -e POSTGRES_PASSWORD=your_secure_password \
  -p 5432:5432 \
  -d postgres:15

# Update .env.local with the connection string
DATABASE_URL="postgresql://ielts_user:your_secure_password@localhost:5432/ielts_certification"
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Make sure PostgreSQL is running
   - Check if the port 5432 is available
   - Verify credentials in `.env.local`

2. **Authentication Failed**
   - Double-check username and password
   - Ensure the user has proper permissions
   - Try connecting manually with `psql`

3. **Database Does Not Exist**
   - Create the database manually as shown above
   - Make sure the database name matches in `.env.local`

### Testing Connection

You can test the database connection manually:

```bash
# Test connection with psql
psql "postgresql://ielts_user:your_secure_password@localhost:5432/ielts_certification"

# If successful, you should see:
# ielts_certification=>
```

## Current System Status

✅ **Fixed Issues:**
- Admin results page now exists and loads properly
- Database connection errors are handled gracefully
- Hydration errors resolved with proper date formatting
- API endpoints return appropriate fallback data

✅ **Working Features (without database):**
- User authentication and sessions
- All admin navigation and UI
- Results entry forms (UI only)
- Certificate generation (mock data)
- Search functionality (UI only)

✅ **Working Features (with database):**
- Full data persistence
- Real candidate and result management
- Actual certificate generation
- Complete search and filtering
- User management and roles

## Next Steps

1. **For Demo/Development**: The system works fine without a database
2. **For Production**: Set up PostgreSQL as described above
3. **For Testing**: Use the Docker option for quick setup

The application is designed to be resilient and will provide a good user experience even without a database connection.
