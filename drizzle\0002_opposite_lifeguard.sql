-- Add photo_data column first (nullable)
ALTER TABLE "candidates" ADD COLUMN "photo_data" text;

-- Add candidate_number column as nullable first
ALTER TABLE "candidates" ADD COLUMN "candidate_number" text;

-- Update existing candidates with sequential candidate numbers using a CTE
WITH numbered_candidates AS (
  SELECT id, LPAD((ROW_NUMBER() OVER (ORDER BY "created_at"))::text, 3, '0') as new_number
  FROM "candidates"
  WHERE "candidate_number" IS NULL
)
UPDATE "candidates"
SET "candidate_number" = numbered_candidates.new_number
FROM numbered_candidates
WHERE "candidates".id = numbered_candidates.id;

-- Now make candidate_number NOT NULL and add unique constraint
ALTER TABLE "candidates" ALTER COLUMN "candidate_number" SET NOT NULL;
ALTER TABLE "candidates" ADD CONSTRAINT "candidates_candidate_number_unique" UNIQUE("candidate_number");