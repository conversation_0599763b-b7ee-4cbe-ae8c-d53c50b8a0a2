CREATE TABLE "test_registrations" (
	"id" text PRIMARY KEY NOT NULL,
	"candidate_id" text NOT NULL,
	"candidate_number" text NOT NULL,
	"test_date" timestamp NOT NULL,
	"test_center" text NOT NULL,
	"status" text DEFAULT 'registered' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "test_registrations_candidate_id_test_date_unique" UNIQUE("candidate_id","test_date"),
	CONSTRAINT "test_registrations_candidate_number_test_date_unique" UNIQUE("candidate_number","test_date")
);
--> statement-breakpoint
ALTER TABLE "candidates" DROP CONSTRAINT "candidates_email_test_date_unique";--> statement-breakpoint
ALTER TABLE "candidates" DROP CONSTRAINT "candidates_passport_number_test_date_unique";--> statement-breakpoint
ALTER TABLE "candidates" DROP CONSTRAINT "candidates_candidate_number_test_date_unique";--> statement-breakpoint
ALTER TABLE "test_results" DROP CONSTRAINT "test_results_candidate_id_candidates_id_fk";
--> statement-breakpoint
ALTER TABLE "test_results" ADD COLUMN "test_registration_id" text NOT NULL;--> statement-breakpoint
ALTER TABLE "test_registrations" ADD CONSTRAINT "test_registrations_candidate_id_candidates_id_fk" FOREIGN KEY ("candidate_id") REFERENCES "public"."candidates"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "test_results" ADD CONSTRAINT "test_results_test_registration_id_test_registrations_id_fk" FOREIGN KEY ("test_registration_id") REFERENCES "public"."test_registrations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "candidates" DROP COLUMN "candidate_number";--> statement-breakpoint
ALTER TABLE "candidates" DROP COLUMN "test_date";--> statement-breakpoint
ALTER TABLE "candidates" DROP COLUMN "test_center";--> statement-breakpoint
ALTER TABLE "test_results" DROP COLUMN "candidate_id";--> statement-breakpoint
ALTER TABLE "candidates" ADD CONSTRAINT "candidates_passport_number_unique" UNIQUE("passport_number");