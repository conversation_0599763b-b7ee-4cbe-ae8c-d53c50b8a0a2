import { config } from 'dotenv';
import postgres from 'postgres';

// Load environment variables
config({ path: '.env.local' });

const connectionString = process.env.DATABASE_URL!;

async function addDuplicateRegistrationConstraints() {
  const sql = postgres(connectionString);
  
  try {
    console.log('🔧 Adding duplicate registration prevention constraints...\n');

    // Check if constraints already exist
    const existingConstraints = await sql`
      SELECT 
        conname as constraint_name,
        contype as constraint_type,
        pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint 
      WHERE conrelid = 'test_registrations'::regclass 
      AND contype = 'u'
      AND conname IN (
        'test_registrations_candidate_id_test_date_unique',
        'test_registrations_candidate_number_test_date_unique'
      )
      ORDER BY conname
    `;

    console.log('📋 Existing constraints:');
    if (existingConstraints.length === 0) {
      console.log('  - No relevant unique constraints found');
    } else {
      existingConstraints.forEach(constraint => {
        console.log(`  - ${constraint.constraint_name}: ${constraint.constraint_definition}`);
      });
    }

    // Add constraint to prevent duplicate registrations for same candidate on same test date
    const candidateTestDateConstraintExists = existingConstraints.some(
      c => c.constraint_name === 'test_registrations_candidate_id_test_date_unique'
    );

    if (!candidateTestDateConstraintExists) {
      console.log('\n➕ Adding candidate-test date uniqueness constraint...');
      await sql`
        ALTER TABLE test_registrations 
        ADD CONSTRAINT test_registrations_candidate_id_test_date_unique 
        UNIQUE (candidate_id, test_date)
      `;
      console.log('✅ Added constraint: test_registrations_candidate_id_test_date_unique');
    } else {
      console.log('\n✅ Candidate-test date uniqueness constraint already exists');
    }

    // Add constraint to ensure candidate numbers are unique per test date
    const candidateNumberConstraintExists = existingConstraints.some(
      c => c.constraint_name === 'test_registrations_candidate_number_test_date_unique'
    );

    if (!candidateNumberConstraintExists) {
      console.log('\n➕ Adding candidate number uniqueness constraint...');
      await sql`
        ALTER TABLE test_registrations 
        ADD CONSTRAINT test_registrations_candidate_number_test_date_unique 
        UNIQUE (candidate_number, test_date)
      `;
      console.log('✅ Added constraint: test_registrations_candidate_number_test_date_unique');
    } else {
      console.log('\n✅ Candidate number uniqueness constraint already exists');
    }

    // Verify all constraints
    console.log('\n🔍 Verifying all unique constraints on test_registrations:');
    const allConstraints = await sql`
      SELECT 
        conname as constraint_name,
        contype as constraint_type,
        pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint 
      WHERE conrelid = 'test_registrations'::regclass 
      AND contype = 'u'
      ORDER BY conname
    `;

    allConstraints.forEach(constraint => {
      console.log(`  ✅ ${constraint.constraint_name}: ${constraint.constraint_definition}`);
    });

    // Test the constraints by checking for any existing violations
    console.log('\n🧪 Testing for existing constraint violations...');
    
    const duplicateRegistrations = await sql`
      SELECT 
        candidate_id, 
        test_date, 
        COUNT(*) as count
      FROM test_registrations 
      GROUP BY candidate_id, test_date 
      HAVING COUNT(*) > 1
    `;

    const duplicateCandidateNumbers = await sql`
      SELECT 
        candidate_number, 
        test_date, 
        COUNT(*) as count
      FROM test_registrations 
      GROUP BY candidate_number, test_date 
      HAVING COUNT(*) > 1
    `;

    if (duplicateRegistrations.length === 0 && duplicateCandidateNumbers.length === 0) {
      console.log('✅ No constraint violations found - database is clean');
    } else {
      console.log('❌ Found constraint violations:');
      if (duplicateRegistrations.length > 0) {
        console.log(`  - ${duplicateRegistrations.length} duplicate candidate registrations for same test date`);
      }
      if (duplicateCandidateNumbers.length > 0) {
        console.log(`  - ${duplicateCandidateNumbers.length} duplicate candidate numbers for same test date`);
      }
    }

    console.log('\n🎉 Duplicate registration prevention constraints successfully configured!');
    console.log('\nBenefits:');
    console.log('  ✅ Prevents duplicate registrations for same candidate on same test date');
    console.log('  ✅ Ensures candidate numbers are unique within each test date');
    console.log('  ✅ Maintains data integrity at the database level');
    console.log('  ✅ Provides clear error messages for duplicate attempts');

  } catch (error) {
    console.error('❌ Error adding constraints:', error);
    throw error;
  } finally {
    await sql.end();
  }
}

// Run the constraint addition
addDuplicateRegistrationConstraints().catch((error) => {
  console.error('Script failed:', error);
  process.exit(1);
});
