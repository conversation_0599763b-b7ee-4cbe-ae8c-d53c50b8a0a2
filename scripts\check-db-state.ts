import { config } from 'dotenv';
import postgres from 'postgres';

// Load environment variables
config({ path: '.env.local' });

const connectionString = process.env.DATABASE_URL!;

async function checkDatabaseState() {
  const sql = postgres(connectionString);
  
  try {
    console.log('🔍 Checking current database state...\n');

    // Check what tables exist
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `;

    console.log('📋 Existing tables:');
    tables.forEach(table => {
      console.log(`  - ${table.table_name}`);
    });

    // Check candidates table structure if it exists
    const candidatesExists = tables.some(t => t.table_name === 'candidates');
    if (candidatesExists) {
      const candidatesColumns = await sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'candidates' 
        ORDER BY ordinal_position;
      `;

      console.log('\n📋 Candidates table structure:');
      candidatesColumns.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
    }

    // Check test_registrations table structure if it exists
    const registrationsExists = tables.some(t => t.table_name === 'test_registrations');
    if (registrationsExists) {
      const registrationsColumns = await sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'test_registrations' 
        ORDER BY ordinal_position;
      `;

      console.log('\n📋 Test registrations table structure:');
      registrationsColumns.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
    }

    // Check test_results table structure
    const resultsExists = tables.some(t => t.table_name === 'test_results');
    if (resultsExists) {
      const resultsColumns = await sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'test_results' 
        ORDER BY ordinal_position;
      `;

      console.log('\n📋 Test results table structure:');
      resultsColumns.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
    }

    // Check constraints
    const constraints = await sql`
      SELECT 
        conname as constraint_name,
        contype as constraint_type,
        conrelid::regclass as table_name,
        pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint 
      WHERE conrelid::regclass::text IN ('candidates', 'test_registrations', 'test_results')
      AND contype IN ('u', 'p', 'f')
      ORDER BY conrelid, conname;
    `;

    console.log('\n🔗 Constraints:');
    constraints.forEach(constraint => {
      const type = constraint.constraint_type === 'p' ? 'PRIMARY KEY' : 
                   constraint.constraint_type === 'u' ? 'UNIQUE' : 
                   constraint.constraint_type === 'f' ? 'FOREIGN KEY' : constraint.constraint_type;
      console.log(`  - ${constraint.table_name}.${constraint.constraint_name} (${type})`);
    });

    // Check data counts
    if (candidatesExists) {
      const candidateCount = await sql`SELECT COUNT(*) as count FROM candidates`;
      console.log(`\n📊 Candidates: ${candidateCount[0].count}`);
    }

    if (registrationsExists) {
      const registrationCount = await sql`SELECT COUNT(*) as count FROM test_registrations`;
      console.log(`📊 Test registrations: ${registrationCount[0].count}`);
    }

    if (resultsExists) {
      const resultCount = await sql`SELECT COUNT(*) as count FROM test_results`;
      console.log(`📊 Test results: ${resultCount[0].count}`);
    }

  } catch (error) {
    console.error('❌ Error checking database state:', error);
    throw error;
  } finally {
    await sql.end();
  }
}

// Run the check
checkDatabaseState().catch((error) => {
  console.error('Database check failed:', error);
  process.exit(1);
});
