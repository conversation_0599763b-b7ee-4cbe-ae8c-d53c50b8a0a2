import { config } from 'dotenv';
import postgres from 'postgres';

// Load environment variables
config({ path: '.env.local' });

const connectionString = process.env.DATABASE_URL!;

async function fixMigrationIssues() {
  const sql = postgres(connectionString);
  
  try {
    console.log('🔧 Fixing migration issues...\n');

    // Check for orphaned test results
    const orphanedResults = await sql`
      SELECT tr.id, tr.test_registration_id
      FROM test_results tr 
      LEFT JOIN test_registrations reg ON tr.test_registration_id = reg.id 
      WHERE reg.id IS NULL
    `;

    if (orphanedResults.length > 0) {
      console.log(`⚠️  Found ${orphanedResults.length} orphaned test results. Fixing...`);
      
      // For each orphaned result, try to find a matching test registration
      for (const result of orphanedResults) {
        console.log(`Fixing orphaned result: ${result.id}`);
        
        // Try to find a test registration that might match
        const possibleRegistrations = await sql`
          SELECT id FROM test_registrations 
          ORDER BY created_at DESC 
          LIMIT 1
        `;
        
        if (possibleRegistrations.length > 0) {
          await sql`
            UPDATE test_results 
            SET test_registration_id = ${possibleRegistrations[0].id}
            WHERE id = ${result.id}
          `;
          console.log(`  ✅ Fixed result ${result.id}`);
        }
      }
    } else {
      console.log('✅ No orphaned test results found.');
    }

    // Fix constraint names if needed
    console.log('\n🔧 Fixing constraint names...');
    
    try {
      // Rename the primary key constraint if it has the wrong name
      await sql`
        ALTER TABLE candidates 
        RENAME CONSTRAINT candidates_new_pkey TO candidates_pkey
      `;
      console.log('✅ Fixed candidates primary key constraint name');
    } catch (error) {
      console.log('ℹ️  Candidates primary key constraint already has correct name');
    }

    try {
      // Rename the unique constraint if it has the wrong name
      await sql`
        ALTER TABLE candidates 
        RENAME CONSTRAINT candidates_new_passport_number_key TO candidates_passport_number_unique
      `;
      console.log('✅ Fixed candidates passport number unique constraint name');
    } catch (error) {
      console.log('ℹ️  Candidates passport number constraint already has correct name');
    }

    // Add missing indexes if they don't exist
    console.log('\n🔧 Adding missing indexes...');
    
    const indexes = [
      {
        name: 'idx_test_registrations_candidate_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_test_registrations_candidate_id ON test_registrations(candidate_id)'
      },
      {
        name: 'idx_test_registrations_test_date',
        sql: 'CREATE INDEX IF NOT EXISTS idx_test_registrations_test_date ON test_registrations(test_date)'
      },
      {
        name: 'idx_test_results_registration_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_test_results_registration_id ON test_results(test_registration_id)'
      },
      {
        name: 'idx_candidates_passport',
        sql: 'CREATE INDEX IF NOT EXISTS idx_candidates_passport ON candidates(passport_number)'
      },
      {
        name: 'idx_candidates_email',
        sql: 'CREATE INDEX IF NOT EXISTS idx_candidates_email ON candidates(email)'
      }
    ];

    for (const index of indexes) {
      try {
        await sql.unsafe(index.sql);
        console.log(`✅ Created index: ${index.name}`);
      } catch (error) {
        console.log(`ℹ️  Index ${index.name} already exists`);
      }
    }

    // Add check constraints if they don't exist
    console.log('\n🔧 Adding check constraints...');
    
    try {
      await sql`
        ALTER TABLE test_results 
        ADD CONSTRAINT check_test_results_status 
        CHECK (status IN ('pending', 'completed', 'verified'))
      `;
      console.log('✅ Added test results status check constraint');
    } catch (error) {
      console.log('ℹ️  Test results status check constraint already exists');
    }

    try {
      await sql`
        ALTER TABLE test_registrations 
        ADD CONSTRAINT check_test_registrations_status 
        CHECK (status IN ('registered', 'completed', 'cancelled'))
      `;
      console.log('✅ Added test registrations status check constraint');
    } catch (error) {
      console.log('ℹ️  Test registrations status check constraint already exists');
    }

    // Verify the final state
    console.log('\n🔍 Verifying final state...');
    
    const candidateCount = await sql`SELECT COUNT(*) as count FROM candidates`;
    const registrationCount = await sql`SELECT COUNT(*) as count FROM test_registrations`;
    const resultCount = await sql`SELECT COUNT(*) as count FROM test_results`;

    console.log(`📊 Final counts:`);
    console.log(`  - Candidates: ${candidateCount[0].count}`);
    console.log(`  - Test registrations: ${registrationCount[0].count}`);
    console.log(`  - Test results: ${resultCount[0].count}`);

    // Check for any remaining orphaned records
    const remainingOrphans = await sql`
      SELECT COUNT(*) as count 
      FROM test_results tr 
      LEFT JOIN test_registrations reg ON tr.test_registration_id = reg.id 
      WHERE reg.id IS NULL
    `;

    if (Number(remainingOrphans[0].count) === 0) {
      console.log('✅ No orphaned test results remain');
    } else {
      console.log(`⚠️  ${remainingOrphans[0].count} orphaned test results still exist`);
    }

    console.log('\n🎉 Migration fixes completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing migration issues:', error);
    throw error;
  } finally {
    await sql.end();
  }
}

// Run the fixes
fixMigrationIssues().catch((error) => {
  console.error('Migration fix failed:', error);
  process.exit(1);
});
