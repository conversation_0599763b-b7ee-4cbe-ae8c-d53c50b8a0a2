-- Comprehensive migration to restructure candidate and test registration schema
-- This migration preserves all existing data while implementing the new structure

BEGIN;

-- Step 1: Create test_registrations table
CREATE TABLE IF NOT EXISTS "test_registrations" (
	"id" text PRIMARY KEY NOT NULL,
	"candidate_id" text NOT NULL,
	"candidate_number" text NOT NULL,
	"test_date" timestamp NOT NULL,
	"test_center" text NOT NULL,
	"status" text DEFAULT 'registered' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "test_registrations_candidate_id_test_date_unique" UNIQUE("candidate_id","test_date"),
	CONSTRAINT "test_registrations_candidate_number_test_date_unique" UNIQUE("candidate_number","test_date")
);

-- Step 2: Create new candidates table structure (temporary)
CREATE TABLE IF NOT EXISTS "candidates_new" (
	"id" text PRIMARY KEY NOT NULL,
	"full_name" text NOT NULL,
	"email" text NOT NULL,
	"phone_number" text NOT NULL,
	"date_of_birth" timestamp NOT NULL,
	"nationality" text NOT NULL,
	"passport_number" text NOT NULL UNIQUE,
	"photo_url" text,
	"photo_data" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Step 3: Migrate unique candidates to new candidates table
-- Group by passport_number to eliminate duplicates
INSERT INTO "candidates_new" (
	"id", "full_name", "email", "phone_number", "date_of_birth", 
	"nationality", "passport_number", "photo_url", "photo_data", 
	"created_at", "updated_at"
)
SELECT DISTINCT ON (passport_number)
	gen_random_uuid()::text as id,
	full_name,
	email,
	phone_number,
	date_of_birth,
	nationality,
	passport_number,
	photo_url,
	photo_data,
	MIN(created_at) as created_at,
	MAX(updated_at) as updated_at
FROM candidates
GROUP BY passport_number, full_name, email, phone_number, date_of_birth, nationality, photo_url, photo_data
ORDER BY passport_number, MIN(created_at);

-- Step 4: Create test registrations for each existing candidate record
INSERT INTO "test_registrations" (
	"id", "candidate_id", "candidate_number", "test_date", 
	"test_center", "status", "created_at", "updated_at"
)
SELECT 
	gen_random_uuid()::text as id,
	cn.id as candidate_id,
	c.candidate_number,
	c.test_date,
	c.test_center,
	CASE 
		WHEN EXISTS (SELECT 1 FROM test_results tr WHERE tr.candidate_id = c.id) 
		THEN 'completed'
		ELSE 'registered'
	END as status,
	c.created_at,
	c.updated_at
FROM candidates c
JOIN candidates_new cn ON c.passport_number = cn.passport_number;

-- Step 5: Add test_registration_id column to test_results
ALTER TABLE "test_results" ADD COLUMN IF NOT EXISTS "test_registration_id" text;

-- Step 6: Update test_results to reference test_registrations
UPDATE test_results 
SET test_registration_id = tr.id
FROM test_registrations tr
JOIN candidates_new cn ON tr.candidate_id = cn.id
JOIN candidates c ON c.passport_number = cn.passport_number
WHERE test_results.candidate_id = c.id 
AND tr.test_date = c.test_date;

-- Step 7: Add foreign key constraints
ALTER TABLE "test_registrations" ADD CONSTRAINT "test_registrations_candidate_id_candidates_new_id_fk" 
	FOREIGN KEY ("candidate_id") REFERENCES "candidates_new"("id") ON DELETE cascade ON UPDATE no action;

-- Step 8: Make test_registration_id NOT NULL after data migration
ALTER TABLE "test_results" ALTER COLUMN "test_registration_id" SET NOT NULL;

ALTER TABLE "test_results" ADD CONSTRAINT "test_results_test_registration_id_test_registrations_id_fk" 
	FOREIGN KEY ("test_registration_id") REFERENCES "test_registrations"("id") ON DELETE cascade ON UPDATE no action;

-- Step 9: Drop old constraints and foreign keys
ALTER TABLE "test_results" DROP CONSTRAINT IF EXISTS "test_results_candidate_id_candidates_id_fk";
ALTER TABLE "candidates" DROP CONSTRAINT IF EXISTS "candidates_email_test_date_unique";
ALTER TABLE "candidates" DROP CONSTRAINT IF EXISTS "candidates_passport_number_test_date_unique";
ALTER TABLE "candidates" DROP CONSTRAINT IF EXISTS "candidates_candidate_number_test_date_unique";

-- Step 10: Drop old columns from test_results
ALTER TABLE "test_results" DROP COLUMN IF EXISTS "candidate_id";

-- Step 11: Replace old candidates table with new one
DROP TABLE IF EXISTS "candidates" CASCADE;
ALTER TABLE "candidates_new" RENAME TO "candidates";

-- Step 12: Update foreign key constraint name
ALTER TABLE "test_registrations" DROP CONSTRAINT IF EXISTS "test_registrations_candidate_id_candidates_new_id_fk";
ALTER TABLE "test_registrations" ADD CONSTRAINT "test_registrations_candidate_id_candidates_id_fk" 
	FOREIGN KEY ("candidate_id") REFERENCES "candidates"("id") ON DELETE cascade ON UPDATE no action;

-- Step 13: Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_test_registrations_candidate_id" ON "test_registrations"("candidate_id");
CREATE INDEX IF NOT EXISTS "idx_test_registrations_test_date" ON "test_registrations"("test_date");
CREATE INDEX IF NOT EXISTS "idx_test_results_registration_id" ON "test_results"("test_registration_id");
CREATE INDEX IF NOT EXISTS "idx_candidates_passport" ON "candidates"("passport_number");
CREATE INDEX IF NOT EXISTS "idx_candidates_email" ON "candidates"("email");

-- Step 14: Add check constraints
ALTER TABLE "test_results" ADD CONSTRAINT "check_test_results_status" 
	CHECK (status IN ('pending', 'completed', 'verified'));

ALTER TABLE "test_registrations" ADD CONSTRAINT "check_test_registrations_status" 
	CHECK (status IN ('registered', 'completed', 'cancelled'));

-- Step 15: Add comments for documentation
COMMENT ON TABLE "candidates" IS 'Core candidate profile information - one record per person';
COMMENT ON TABLE "test_registrations" IS 'Individual test registrations for candidates - allows multiple tests per candidate';
COMMENT ON TABLE "test_results" IS 'Test results linked to specific test registrations';

COMMIT;
