-- Migration to separate candidates from test registrations
-- This migration restructures the database to have proper candidate profiles
-- with separate test registrations, preventing duplicate candidate records

-- Step 1: Create new candidates table (core profile information)
CREATE TABLE IF NOT EXISTS candidates_new (
    id text PRIMARY KEY,
    full_name text NOT NULL,
    email text NOT NULL,
    phone_number text NOT NULL,
    date_of_birth timestamp NOT NULL,
    nationality text NOT NULL,
    passport_number text NOT NULL UNIQUE,
    photo_url text,
    photo_data text,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL
);

-- Step 2: Create test registrations table
CREATE TABLE IF NOT EXISTS test_registrations (
    id text PRIMARY KEY,
    candidate_id text NOT NULL,
    candidate_number text NOT NULL,
    test_date timestamp NOT NULL,
    test_center text NOT NULL,
    status text DEFAULT 'registered' NOT NULL,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL,
    CONSTRAINT fk_test_registrations_candidate 
        <PERSON>OR<PERSON><PERSON><PERSON> KEY (candidate_id) REFERENCES candidates_new(id) ON DELETE CASCADE,
    CONSTRAINT unique_candidate_test_date 
        UNIQUE (candidate_id, test_date),
    CONSTRAINT unique_candidate_number_test_date 
        UNIQUE (candidate_number, test_date)
);

-- Step 3: Migrate existing data
-- First, insert unique candidates (deduplicated by passport_number)
INSERT INTO candidates_new (id, full_name, email, phone_number, date_of_birth, nationality, passport_number, photo_url, photo_data, created_at, updated_at)
SELECT DISTINCT ON (passport_number)
    gen_random_uuid()::text as id,
    full_name,
    email,
    phone_number,
    date_of_birth,
    nationality,
    passport_number,
    photo_url,
    photo_data,
    MIN(created_at) as created_at,
    MAX(updated_at) as updated_at
FROM candidates
GROUP BY passport_number, full_name, email, phone_number, date_of_birth, nationality, photo_url, photo_data
ORDER BY passport_number, MIN(created_at);

-- Step 4: Create test registrations for each existing candidate record
INSERT INTO test_registrations (id, candidate_id, candidate_number, test_date, test_center, status, created_at, updated_at)
SELECT 
    gen_random_uuid()::text as id,
    cn.id as candidate_id,
    c.candidate_number,
    c.test_date,
    c.test_center,
    CASE 
        WHEN EXISTS (SELECT 1 FROM test_results tr WHERE tr.candidate_id = c.id) 
        THEN 'completed'
        ELSE 'registered'
    END as status,
    c.created_at,
    c.updated_at
FROM candidates c
JOIN candidates_new cn ON c.passport_number = cn.passport_number;

-- Step 5: Create new test_results table with updated foreign key
CREATE TABLE IF NOT EXISTS test_results_new (
    id text PRIMARY KEY,
    test_registration_id text NOT NULL,
    listening_score numeric(3, 1),
    listening_band_score numeric(2, 1),
    reading_score numeric(3, 1),
    reading_band_score numeric(2, 1),
    writing_task1_score numeric(2, 1),
    writing_task2_score numeric(2, 1),
    writing_band_score numeric(2, 1),
    speaking_fluency_score numeric(2, 1),
    speaking_lexical_score numeric(2, 1),
    speaking_grammar_score numeric(2, 1),
    speaking_pronunciation_score numeric(2, 1),
    speaking_band_score numeric(2, 1),
    overall_band_score numeric(2, 1),
    status text DEFAULT 'pending' NOT NULL,
    entered_by text,
    verified_by text,
    certificate_generated boolean DEFAULT false,
    certificate_serial text UNIQUE,
    certificate_url text,
    ai_feedback_generated boolean DEFAULT false,
    test_date timestamp,
    created_at timestamp DEFAULT now() NOT NULL,
    updated_at timestamp DEFAULT now() NOT NULL,
    CONSTRAINT fk_test_results_registration 
        FOREIGN KEY (test_registration_id) REFERENCES test_registrations(id) ON DELETE CASCADE,
    CONSTRAINT fk_test_results_entered_by 
        FOREIGN KEY (entered_by) REFERENCES users(id),
    CONSTRAINT fk_test_results_verified_by 
        FOREIGN KEY (verified_by) REFERENCES users(id)
);

-- Step 6: Migrate test results data
INSERT INTO test_results_new (
    id, test_registration_id, listening_score, listening_band_score, reading_score, reading_band_score,
    writing_task1_score, writing_task2_score, writing_band_score, speaking_fluency_score,
    speaking_lexical_score, speaking_grammar_score, speaking_pronunciation_score, speaking_band_score,
    overall_band_score, status, entered_by, verified_by, certificate_generated, certificate_serial,
    certificate_url, ai_feedback_generated, test_date, created_at, updated_at
)
SELECT 
    tr.id,
    treg.id as test_registration_id,
    tr.listening_score,
    tr.listening_band_score,
    tr.reading_score,
    tr.reading_band_score,
    tr.writing_task1_score,
    tr.writing_task2_score,
    tr.writing_band_score,
    tr.speaking_fluency_score,
    tr.speaking_lexical_score,
    tr.speaking_grammar_score,
    tr.speaking_pronunciation_score,
    tr.speaking_band_score,
    tr.overall_band_score,
    tr.status,
    tr.entered_by,
    tr.verified_by,
    tr.certificate_generated,
    tr.certificate_serial,
    tr.certificate_url,
    tr.ai_feedback_generated,
    tr.test_date,
    tr.created_at,
    tr.updated_at
FROM test_results tr
JOIN candidates c ON tr.candidate_id = c.id
JOIN candidates_new cn ON c.passport_number = cn.passport_number
JOIN test_registrations treg ON treg.candidate_id = cn.id AND treg.test_date = c.test_date;

-- Step 7: Update AI feedback table to reference new test results
UPDATE ai_feedback 
SET test_result_id = trn.id
FROM test_results_new trn
WHERE ai_feedback.test_result_id = trn.id;

-- Step 8: Drop old tables and rename new ones
DROP TABLE IF EXISTS test_results CASCADE;
DROP TABLE IF EXISTS candidates CASCADE;

ALTER TABLE candidates_new RENAME TO candidates;
ALTER TABLE test_results_new RENAME TO test_results;

-- Step 9: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_test_registrations_candidate_id ON test_registrations(candidate_id);
CREATE INDEX IF NOT EXISTS idx_test_registrations_test_date ON test_registrations(test_date);
CREATE INDEX IF NOT EXISTS idx_test_results_registration_id ON test_results(test_registration_id);
CREATE INDEX IF NOT EXISTS idx_candidates_passport ON candidates(passport_number);
CREATE INDEX IF NOT EXISTS idx_candidates_email ON candidates(email);

-- Step 10: Add constraints that might have been lost
ALTER TABLE test_results ADD CONSTRAINT check_status 
    CHECK (status IN ('pending', 'completed', 'verified'));

ALTER TABLE test_registrations ADD CONSTRAINT check_registration_status 
    CHECK (status IN ('registered', 'completed', 'cancelled'));

COMMENT ON TABLE candidates IS 'Core candidate profile information - one record per person';
COMMENT ON TABLE test_registrations IS 'Individual test registrations for candidates - allows multiple tests per candidate';
COMMENT ON TABLE test_results IS 'Test results linked to specific test registrations';
