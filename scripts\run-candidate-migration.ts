import { config } from 'dotenv';
import postgres from 'postgres';
import { readFileSync } from 'fs';
import { join } from 'path';

// Load environment variables
config({ path: '.env.local' });

const connectionString = process.env.DATABASE_URL!;

async function runMigration() {
  const sql = postgres(connectionString);

  try {
    console.log('🚀 Starting candidate schema migration...\n');

    // Read the migration SQL file
    const migrationPath = join(process.cwd(), 'scripts', 'migrate-candidate-schema.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf-8');

    console.log('📄 Executing migration SQL...');

    // Split the SQL into individual statements and execute them
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && stmt !== 'BEGIN' && stmt !== 'COMMIT');

    // Execute the migration in a transaction
    await sql.begin(async sql => {
      for (const statement of statements) {
        if (statement.trim()) {
          console.log(`Executing: ${statement.substring(0, 50)}...`);
          await sql.unsafe(statement);
        }
      }
    });

    console.log('✅ Migration completed successfully!\n');

    // Verify the migration by checking table structures
    console.log('🔍 Verifying migration results...\n');

    // Check candidates table structure
    const candidatesColumns = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'candidates'
      ORDER BY ordinal_position;
    `;

    console.log('📋 Candidates table structure:');
    candidatesColumns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });

    // Check test_registrations table structure
    const registrationsColumns = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'test_registrations'
      ORDER BY ordinal_position;
    `;

    console.log('\n📋 Test registrations table structure:');
    registrationsColumns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });

    // Check test_results table structure
    const resultsColumns = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'test_results'
      AND column_name IN ('test_registration_id', 'candidate_id')
      ORDER BY ordinal_position;
    `;

    console.log('\n📋 Test results table (relevant columns):');
    resultsColumns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });

    // Check constraints
    const constraints = await sql`
      SELECT
        conname as constraint_name,
        contype as constraint_type,
        pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint
      WHERE conrelid IN ('candidates'::regclass, 'test_registrations'::regclass)
      AND contype IN ('u', 'p', 'f')
      ORDER BY conrelid, conname;
    `;

    console.log('\n🔗 Constraints:');
    constraints.forEach(constraint => {
      const type = constraint.constraint_type === 'p' ? 'PRIMARY KEY' :
                   constraint.constraint_type === 'u' ? 'UNIQUE' :
                   constraint.constraint_type === 'f' ? 'FOREIGN KEY' : constraint.constraint_type;
      console.log(`  - ${constraint.constraint_name} (${type}): ${constraint.constraint_definition}`);
    });

    // Check data counts
    const candidateCount = await sql`SELECT COUNT(*) as count FROM candidates`;
    const registrationCount = await sql`SELECT COUNT(*) as count FROM test_registrations`;
    const resultCount = await sql`SELECT COUNT(*) as count FROM test_results`;

    console.log('\n📊 Data counts:');
    console.log(`  - Candidates: ${candidateCount[0].count}`);
    console.log(`  - Test registrations: ${registrationCount[0].count}`);
    console.log(`  - Test results: ${resultCount[0].count}`);

    // Check for any orphaned records
    const orphanedResults = await sql`
      SELECT COUNT(*) as count
      FROM test_results tr
      LEFT JOIN test_registrations reg ON tr.test_registration_id = reg.id
      WHERE reg.id IS NULL
    `;

    if (Number(orphanedResults[0].count) > 0) {
      console.log(`\n⚠️  Warning: ${orphanedResults[0].count} orphaned test results found!`);
    } else {
      console.log('\n✅ No orphaned test results found.');
    }

    console.log('\n🎉 Migration verification completed successfully!');
    console.log('\n📝 Summary:');
    console.log('  - Candidates table now stores core profile information');
    console.log('  - Test registrations table manages individual test registrations');
    console.log('  - Test results are linked to test registrations');
    console.log('  - Duplicate candidate records have been consolidated');
    console.log('  - All existing data has been preserved');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await sql.end();
  }
}

// Run the migration
runMigration().catch((error) => {
  console.error('Migration script failed:', error);
  process.exit(1);
});
