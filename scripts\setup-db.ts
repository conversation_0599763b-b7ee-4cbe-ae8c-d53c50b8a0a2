import { db } from '../src/lib/db';
import { users } from '../src/lib/db/schema';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcryptjs';

async function setupDatabase() {
  try {
    console.log('Setting up database...');

    // Create admin user
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
    const hashedAdminPassword = await bcrypt.hash(adminPassword, 10);

    // Check if admin user already exists
    const existingAdmin = await db
      .select()
      .from(users)
      .where(eq(users.email, adminEmail))
      .limit(1);

    if (existingAdmin.length === 0) {
      await db.insert(users).values({
        name: 'System Administrator',
        email: adminEmail,
        password: hashedAdminPassword,
        role: 'admin',
      });
      console.log('✅ Admin user created');
    } else {
      console.log('ℹ️ Admin user already exists');
    }

    // Create test checker user
    const checkerEmail = '<EMAIL>';
    const checkerPassword = 'checker123';
    const hashedCheckerPassword = await bcrypt.hash(checkerPassword, 10);

    const existingChecker = await db
      .select()
      .from(users)
      .where(eq(users.email, checkerEmail))
      .limit(1);

    if (existingChecker.length === 0) {
      await db.insert(users).values({
        name: 'Test Checker',
        email: checkerEmail,
        password: hashedCheckerPassword,
        role: 'test_checker',
      });
      console.log('✅ Test checker user created');
    } else {
      console.log('ℹ️ Test checker user already exists');
    }

    console.log('✅ Database setup completed!');
    console.log('\nDemo credentials:');
    console.log(`Admin: ${adminEmail} / ${adminPassword}`);
    console.log(`Test Checker: ${checkerEmail} / checker123`);

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

// Run the setup
setupDatabase();
