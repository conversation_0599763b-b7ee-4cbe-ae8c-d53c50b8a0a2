import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables
config({ path: resolve(process.cwd(), '.env.local') });

async function testDatabaseConnection() {
  console.log('🔍 Testing Database Connection...\n');

  // Check environment variables
  console.log('1️⃣ Checking environment variables:');
  const dbUrl = process.env.DATABASE_URL;

  if (!dbUrl) {
    console.log('❌ DATABASE_URL not found in environment variables');
    console.log('📝 Please ensure .env.local file exists with DATABASE_URL');
    console.log('📝 Example: DATABASE_URL="postgresql://username:password@localhost:5432/database_name"');
    return;
  }

  console.log('✅ DATABASE_URL found');

  // Parse database URL (safely, without exposing password)
  try {
    const url = new URL(dbUrl);
    console.log(`   Host: ${url.hostname}`);
    console.log(`   Port: ${url.port || '5432'}`);
    console.log(`   Database: ${url.pathname.slice(1)}`);
    console.log(`   Username: ${url.username}`);
    console.log(`   Password: ${url.password ? '[HIDDEN]' : '[NOT SET]'}`);
  } catch (error) {
    console.log('❌ Invalid DATABASE_URL format');
    console.log('📝 Expected format: postgresql://username:password@host:port/database');
    return;
  }

  // Test basic connection
  console.log('\n2️⃣ Testing database connection:');

  try {
    // Try to import and use the database connection
    const { db } = await import('../src/lib/db');

    // Simple query to test connection
    const result = await db.execute('SELECT 1 as test');
    console.log('✅ Database connection successful');
    console.log(`   Test query result: ${JSON.stringify(result)}`);

  } catch (error) {
    console.log('❌ Database connection failed:');
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.log(`   Error: ${errorMessage}`);

    if (errorMessage.includes('28P01')) {
      console.log('\n🔧 Authentication Error (28P01):');
      console.log('   - Check username and password in DATABASE_URL');
      console.log('   - Ensure the database user exists');
      console.log('   - Verify user has proper permissions');
    } else if (errorMessage.includes('ECONNREFUSED')) {
      console.log('\n🔧 Connection Refused:');
      console.log('   - Check if PostgreSQL is running');
      console.log('   - Verify host and port are correct');
      console.log('   - Check firewall settings');
    } else if (errorMessage.includes('database') && errorMessage.includes('does not exist')) {
      console.log('\n🔧 Database Does Not Exist:');
      console.log('   - Create the database first');
      console.log('   - Run: createdb your_database_name');
    }

    return;
  }

  // Test schema existence
  console.log('\n3️⃣ Checking database schema:');

  try {
    const { db } = await import('../src/lib/db');

    // Check if main tables exist
    const tables = await db.execute(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name IN ('candidates', 'test_registrations', 'test_results')
      ORDER BY table_name
    `);

    console.log(`✅ Found ${tables.length} main tables:`);
    tables.forEach((table: any) => {
      console.log(`   - ${table.table_name}`);
    });

    if (tables.length < 3) {
      console.log('\n🔧 Missing tables detected:');
      console.log('   - Run database migrations: npm run db:migrate');
      console.log('   - Or run setup script: npm run db:setup');
    }

  } catch (error) {
    console.log('❌ Schema check failed:');
    console.log(`   Error: ${error instanceof Error ? error.message : String(error)}`);
  }

  // Test data existence
  console.log('\n4️⃣ Checking for existing data:');

  try {
    const { db } = await import('../src/lib/db');
    const { candidates, testRegistrations, testResults } = await import('../src/lib/db/schema');
    const { count } = await import('drizzle-orm');

    const candidatesCount = await db.select({ count: count() }).from(candidates);
    const registrationsCount = await db.select({ count: count() }).from(testRegistrations);
    const resultsCount = await db.select({ count: count() }).from(testResults);

    console.log('✅ Data counts:');
    console.log(`   - Candidates: ${candidatesCount[0]?.count || 0}`);
    console.log(`   - Test Registrations: ${registrationsCount[0]?.count || 0}`);
    console.log(`   - Test Results: ${resultsCount[0]?.count || 0}`);

  } catch (error) {
    console.log('❌ Data check failed:');
    console.log(`   Error: ${error instanceof Error ? error.message : String(error)}`);
  }

  console.log('\n🎉 Database connection test completed!');
  console.log('\n📋 Next steps:');
  console.log('   1. If connection failed, fix the database setup');
  console.log('   2. If tables are missing, run migrations');
  console.log('   3. If data is missing, run the setup script');
  console.log('   4. Then run the data integrity check');
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Test interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Test terminated');
  process.exit(0);
});

// Run the test
testDatabaseConnection()
  .then(() => {
    console.log('\n✅ Test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  });
