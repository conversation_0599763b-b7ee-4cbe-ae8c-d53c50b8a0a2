# PowerShell script to test IELTS API endpoints

$baseUrl = "http://localhost:3001"

Write-Host "🚀 Testing IELTS API Endpoints" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Test 1: Health Check
Write-Host "`n📋 Testing Health Check..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/health" -Method GET
    Write-Host "✅ Health Check: " -ForegroundColor Green -NoNewline
    Write-Host ($response | ConvertTo-Json -Depth 2)
} catch {
    Write-Host "❌ Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Search Endpoint
Write-Host "`n🔍 Testing Search Endpoint..." -ForegroundColor Yellow
try {
    $searchBody = @{
        query = "test"
        searchType = "name"
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri "$baseUrl/api/search" -Method POST -Body $searchBody -ContentType "application/json"
    Write-Host "✅ Search Endpoint: " -ForegroundColor Green -NoNewline
    Write-Host ($response | ConvertTo-Json -Depth 2)
} catch {
    Write-Host "❌ Search Endpoint Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Public Result Access (will fail but should show proper error)
Write-Host "`n📊 Testing Public Result Access..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/results/test-id" -Method GET
    Write-Host "✅ Result Access: " -ForegroundColor Green -NoNewline
    Write-Host ($response | ConvertTo-Json -Depth 2)
} catch {
    Write-Host "❌ Result Access (Expected): $($_.Exception.Message)" -ForegroundColor Yellow
}

# Test 4: Certificate Verification
Write-Host "`n🏆 Testing Certificate Verification..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/certificate/verify/IELTS-2024-123456" -Method GET
    Write-Host "✅ Certificate Verification: " -ForegroundColor Green -NoNewline
    Write-Host ($response | ConvertTo-Json -Depth 2)
} catch {
    Write-Host "❌ Certificate Verification (Expected): $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n✅ API Endpoint Testing Complete!" -ForegroundColor Green
Write-Host "Note: Some failures are expected due to missing test data." -ForegroundColor Cyan
