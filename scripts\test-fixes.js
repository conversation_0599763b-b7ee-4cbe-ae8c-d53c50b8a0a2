/**
 * Test script to verify the fixes for IELTS system issues
 */

const BASE_URL = 'http://localhost:3001';

async function testEndpoint(url, options = {}) {
  try {
    console.log(`\n🧪 Testing: ${options.method || 'GET'} ${url}`);
    const response = await fetch(url, options);
    
    console.log(`   Status: ${response.status}`);
    
    if (response.headers.get('content-type')?.includes('application/json')) {
      const data = await response.json();
      console.log(`   Response:`, JSON.stringify(data, null, 2));
      return { success: response.ok, status: response.status, data };
    } else {
      const text = await response.text();
      console.log(`   Response: ${text.substring(0, 200)}...`);
      return { success: response.ok, status: response.status, text };
    }
  } catch (error) {
    console.log(`   ❌ Error:`, error.message);
    return { success: false, error: error.message };
  }
}

async function runFixTests() {
  console.log('🚀 Testing IELTS System Fixes\n');
  
  // Test 1: Health Check
  console.log('='.repeat(60));
  console.log('TEST 1: Health Check (Should work now)');
  console.log('='.repeat(60));
  
  const healthCheck = await testEndpoint(`${BASE_URL}/api/health`);
  if (!healthCheck.success) {
    console.log('❌ Health check failed - server may not be running properly');
  } else {
    console.log('✅ Health check passed');
  }
  
  // Test 2: Public Search (Should work without auth)
  console.log('\n' + '='.repeat(60));
  console.log('TEST 2: Public Search (No Auth Required)');
  console.log('='.repeat(60));
  
  const searchResult = await testEndpoint(`${BASE_URL}/api/search`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: 'test',
      searchType: 'name'
    })
  });
  
  if (searchResult.success) {
    console.log('✅ Public search endpoint working');
  } else {
    console.log('❌ Public search endpoint failed');
  }
  
  // Test 3: Public Results Access (Should work without auth)
  console.log('\n' + '='.repeat(60));
  console.log('TEST 3: Public Results Access (No Auth Required)');
  console.log('='.repeat(60));
  
  // Test with a sample ID (will likely return 404 but should not return 401)
  const resultAccess = await testEndpoint(`${BASE_URL}/api/results/sample-id`);
  
  if (resultAccess.status === 404) {
    console.log('✅ Public results access working (404 expected for non-existent ID)');
  } else if (resultAccess.status === 401) {
    console.log('❌ Public results access still requires authentication');
  } else {
    console.log(`ℹ️  Public results access returned status: ${resultAccess.status}`);
  }
  
  // Test 4: Public Feedback Access (Should work without auth)
  console.log('\n' + '='.repeat(60));
  console.log('TEST 4: Public Feedback Access (No Auth Required)');
  console.log('='.repeat(60));
  
  const feedbackAccess = await testEndpoint(`${BASE_URL}/api/feedback/sample-id`);
  
  if (feedbackAccess.status === 404) {
    console.log('✅ Public feedback access working (404 expected for non-existent ID)');
  } else if (feedbackAccess.status === 401) {
    console.log('❌ Public feedback access still requires authentication');
  } else {
    console.log(`ℹ️  Public feedback access returned status: ${feedbackAccess.status}`);
  }
  
  // Test 5: Certificate Verification (Should work without auth)
  console.log('\n' + '='.repeat(60));
  console.log('TEST 5: Certificate Verification (No Auth Required)');
  console.log('='.repeat(60));
  
  const certVerification = await testEndpoint(`${BASE_URL}/api/certificate/verify/IELTS-2024-123456`);
  
  if (certVerification.status === 404) {
    console.log('✅ Certificate verification working (404 expected for non-existent serial)');
  } else if (certVerification.status === 401) {
    console.log('❌ Certificate verification still requires authentication');
  } else {
    console.log(`ℹ️  Certificate verification returned status: ${certVerification.status}`);
  }
  
  // Test 6: Public Results Page Access (Frontend)
  console.log('\n' + '='.repeat(60));
  console.log('TEST 6: Public Results Page Access (Frontend)');
  console.log('='.repeat(60));
  
  const resultsPageAccess = await testEndpoint(`${BASE_URL}/results/sample-id`);
  
  if (resultsPageAccess.success || resultsPageAccess.status === 404) {
    console.log('✅ Public results page accessible (no redirect to login)');
  } else if (resultsPageAccess.status === 302 || resultsPageAccess.status === 307) {
    console.log('❌ Public results page redirecting (likely to login)');
  } else {
    console.log(`ℹ️  Public results page returned status: ${resultsPageAccess.status}`);
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('✅ Fix Testing Complete!');
  console.log('='.repeat(60));
  console.log('\nSummary:');
  console.log('- Health check endpoint created');
  console.log('- Middleware updated to allow public access to results pages');
  console.log('- API validation fixed to accept string IDs instead of numeric only');
  console.log('- Certificate download made public (no auth required)');
  console.log('- Automatic AI feedback and certificate generation added to Quick Entry');
  console.log('\nNext: Test with real data by adding candidates and entering results!');
}

// Run the tests
runFixTests().catch(console.error);
