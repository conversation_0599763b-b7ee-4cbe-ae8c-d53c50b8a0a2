import { config } from 'dotenv';
import postgres from 'postgres';

// Load environment variables
config({ path: '.env.local' });

const connectionString = process.env.DATABASE_URL!;

async function testNewRegistrationSystem() {
  const sql = postgres(connectionString);
  
  try {
    console.log('🧪 Testing new candidate registration system...\n');

    // Test 1: Check current state
    console.log('📊 Current database state:');
    const candidateCount = await sql`SELECT COUNT(*) as count FROM candidates`;
    const registrationCount = await sql`SELECT COUNT(*) as count FROM test_registrations`;
    const resultCount = await sql`SELECT COUNT(*) as count FROM test_results`;

    console.log(`  - Candidates: ${candidateCount[0].count}`);
    console.log(`  - Test registrations: ${registrationCount[0].count}`);
    console.log(`  - Test results: ${resultCount[0].count}`);

    // Test 2: Check for duplicate candidates (should be none)
    console.log('\n🔍 Checking for duplicate candidates...');
    const duplicates = await sql`
      SELECT passport_number, COUNT(*) as count 
      FROM candidates 
      GROUP BY passport_number 
      HAVING COUNT(*) > 1
    `;

    if (duplicates.length === 0) {
      console.log('✅ No duplicate candidates found');
    } else {
      console.log(`❌ Found ${duplicates.length} duplicate passport numbers:`);
      duplicates.forEach(dup => {
        console.log(`  - ${dup.passport_number}: ${dup.count} records`);
      });
    }

    // Test 3: Check candidate with multiple test registrations
    console.log('\n🔍 Checking candidates with multiple test registrations...');
    const multipleRegistrations = await sql`
      SELECT 
        c.full_name,
        c.passport_number,
        COUNT(tr.id) as registration_count
      FROM candidates c
      LEFT JOIN test_registrations tr ON c.id = tr.candidate_id
      GROUP BY c.id, c.full_name, c.passport_number
      HAVING COUNT(tr.id) > 1
      ORDER BY registration_count DESC
    `;

    if (multipleRegistrations.length > 0) {
      console.log('✅ Found candidates with multiple registrations:');
      multipleRegistrations.forEach(candidate => {
        console.log(`  - ${candidate.full_name} (${candidate.passport_number}): ${candidate.registration_count} registrations`);
      });
    } else {
      console.log('ℹ️  No candidates with multiple registrations found');
    }

    // Test 4: Check test results linkage
    console.log('\n🔍 Checking test results linkage...');
    const resultsWithRegistrations = await sql`
      SELECT 
        tr.id as result_id,
        reg.candidate_number,
        reg.test_date,
        c.full_name
      FROM test_results tr
      JOIN test_registrations reg ON tr.test_registration_id = reg.id
      JOIN candidates c ON reg.candidate_id = c.id
      ORDER BY reg.test_date DESC
    `;

    console.log(`✅ Found ${resultsWithRegistrations.length} test results properly linked:`);
    resultsWithRegistrations.forEach(result => {
      console.log(`  - ${result.full_name} (${result.candidate_number}) - ${result.test_date.toISOString().split('T')[0]}`);
    });

    // Test 5: Check for orphaned records
    console.log('\n🔍 Checking for orphaned records...');
    
    const orphanedResults = await sql`
      SELECT COUNT(*) as count 
      FROM test_results tr 
      LEFT JOIN test_registrations reg ON tr.test_registration_id = reg.id 
      WHERE reg.id IS NULL
    `;

    const orphanedRegistrations = await sql`
      SELECT COUNT(*) as count 
      FROM test_registrations tr 
      LEFT JOIN candidates c ON tr.candidate_id = c.id 
      WHERE c.id IS NULL
    `;

    if (Number(orphanedResults[0].count) === 0 && Number(orphanedRegistrations[0].count) === 0) {
      console.log('✅ No orphaned records found');
    } else {
      console.log(`❌ Found orphaned records:`);
      console.log(`  - Orphaned test results: ${orphanedResults[0].count}`);
      console.log(`  - Orphaned test registrations: ${orphanedRegistrations[0].count}`);
    }

    // Test 6: Simulate new candidate registration
    console.log('\n🧪 Simulating new candidate registration...');
    
    const testPassport = 'TEST' + Date.now();
    const testDate = new Date('2025-02-02'); // Next Sunday
    
    // Check if candidate exists
    const existingCandidate = await sql`
      SELECT id FROM candidates WHERE passport_number = ${testPassport}
    `;

    let candidateId: string;
    
    if (existingCandidate.length === 0) {
      // Create new candidate
      const newCandidate = await sql`
        INSERT INTO candidates (
          id, full_name, email, phone_number, date_of_birth, 
          nationality, passport_number
        ) VALUES (
          gen_random_uuid()::text,
          'Test Candidate',
          '<EMAIL>',
          '+1234567890',
          '1990-01-01',
          'Test Country',
          ${testPassport}
        ) RETURNING id
      `;
      candidateId = newCandidate[0].id;
      console.log(`✅ Created new candidate: ${candidateId}`);
    } else {
      candidateId = existingCandidate[0].id;
      console.log(`ℹ️  Using existing candidate: ${candidateId}`);
    }

    // Generate candidate number
    const existingRegistrations = await sql`
      SELECT COUNT(*) as count FROM test_registrations WHERE test_date = ${testDate}
    `;
    const candidateNumber = (Number(existingRegistrations[0].count) + 1).toString().padStart(3, '0');

    // Create test registration
    const newRegistration = await sql`
      INSERT INTO test_registrations (
        id, candidate_id, candidate_number, test_date, test_center, status
      ) VALUES (
        gen_random_uuid()::text,
        ${candidateId},
        ${candidateNumber},
        ${testDate},
        'Innovative Centre - Samarkand',
        'registered'
      ) RETURNING id, candidate_number
    `;

    console.log(`✅ Created test registration: ${newRegistration[0].id} (Number: ${newRegistration[0].candidate_number})`);

    // Clean up test data
    await sql`DELETE FROM test_registrations WHERE candidate_id = ${candidateId}`;
    await sql`DELETE FROM candidates WHERE id = ${candidateId}`;
    console.log('🧹 Cleaned up test data');

    console.log('\n🎉 All tests passed! The new registration system is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    await sql.end();
  }
}

// Run the test
testNewRegistrationSystem().catch((error) => {
  console.error('Test script failed:', error);
  process.exit(1);
});
