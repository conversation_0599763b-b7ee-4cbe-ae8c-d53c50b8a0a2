import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

// Load environment variables
config({ path: '.env.local' });

const connectionString = process.env.DATABASE_URL!;
const sql = postgres(connectionString);
const db = drizzle(sql);

async function verifyMigration() {
  try {
    console.log('🔍 Verifying database migration...\n');

    // Check current constraints on candidates table
    const constraintsQuery = `
      SELECT 
        conname as constraint_name,
        contype as constraint_type,
        pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint 
      WHERE conrelid = 'candidates'::regclass
      AND contype IN ('u', 'p')
      ORDER BY conname;
    `;

    const constraints = await sql.unsafe(constraintsQuery);
    
    console.log('📋 Current constraints on candidates table:');
    console.log('=' .repeat(80));
    
    constraints.forEach((constraint: any) => {
      const type = constraint.constraint_type === 'p' ? 'PRIMARY KEY' : 'UNIQUE';
      console.log(`${constraint.constraint_name} (${type})`);
      console.log(`  Definition: ${constraint.constraint_definition}`);
      console.log('');
    });

    // Check if old constraints are removed
    const oldEmailConstraint = constraints.find((c: any) => c.constraint_name === 'candidates_email_unique');
    const oldPassportConstraint = constraints.find((c: any) => c.constraint_name === 'candidates_passport_number_unique');
    
    // Check if new constraints are added
    const newEmailTestDateConstraint = constraints.find((c: any) => c.constraint_name === 'candidates_email_test_date_unique');
    const newPassportTestDateConstraint = constraints.find((c: any) => c.constraint_name === 'candidates_passport_number_test_date_unique');

    console.log('✅ Migration Verification Results:');
    console.log('=' .repeat(50));
    
    if (!oldEmailConstraint) {
      console.log('✅ Old email unique constraint removed');
    } else {
      console.log('❌ Old email unique constraint still exists');
    }
    
    if (!oldPassportConstraint) {
      console.log('✅ Old passport unique constraint removed');
    } else {
      console.log('❌ Old passport unique constraint still exists');
    }
    
    if (newEmailTestDateConstraint) {
      console.log('✅ New email+test_date unique constraint added');
    } else {
      console.log('❌ New email+test_date unique constraint missing');
    }
    
    if (newPassportTestDateConstraint) {
      console.log('✅ New passport+test_date unique constraint added');
    } else {
      console.log('❌ New passport+test_date unique constraint missing');
    }

    const allGood = !oldEmailConstraint && !oldPassportConstraint && 
                   newEmailTestDateConstraint && newPassportTestDateConstraint;

    console.log('\n' + '=' .repeat(50));
    if (allGood) {
      console.log('🎉 Migration completed successfully!');
      console.log('✅ Existing candidates can now register for multiple test dates');
      console.log('✅ Duplicate registrations for same test date are prevented');
    } else {
      console.log('⚠️  Migration may not have completed correctly');
      console.log('Please check the constraints above');
    }

  } catch (error) {
    console.error('❌ Error verifying migration:', error);
  } finally {
    await sql.end();
  }
}

verifyMigration();
