'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeft,
  Save,
  Calculator,
  Headphones,
  BookOpen,
  PenTool,
  Mic,
  User,
  AlertCircle
} from 'lucide-react';

interface TestResult {
  id: string;
  candidateId: string;
  listeningScore: number | null;
  listeningBandScore: number | null;
  readingScore: number | null;
  readingBandScore: number | null;
  writingTask1Score: number | null;
  writingTask2Score: number | null;
  writingBandScore: number | null;
  speakingFluencyScore: number | null;
  speakingLexicalScore: number | null;
  speakingGrammarScore: number | null;
  speakingPronunciationScore: number | null;
  speakingBandScore: number | null;
  overallBandScore: number | null;
  status: 'pending' | 'completed' | 'verified';
  candidate: {
    fullName: string;
    passportNumber: string;
    testDate: string;
  };
}

export default function AdminEditResultPage() {
  const params = useParams();
  const router = useRouter();
  const resultId = params.id as string;

  const [result, setResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    listeningScore: '',
    listeningBandScore: '',
    readingScore: '',
    readingBandScore: '',
    writingTask1Score: '',
    writingTask2Score: '',
    writingBandScore: '',
    speakingFluencyScore: '',
    speakingLexicalScore: '',
    speakingGrammarScore: '',
    speakingPronunciationScore: '',
    speakingBandScore: '',
    overallBandScore: '',
    status: 'pending' as 'pending' | 'completed' | 'verified',
  });

  const fetchResult = useCallback(async () => {
    try {
      const response = await fetch(`/api/results/${resultId}`);
      if (response.ok) {
        const data = await response.json();
        setResult(data);

        // Populate form data
        setFormData({
          listeningScore: data.listeningScore?.toString() || '',
          listeningBandScore: data.listeningBandScore?.toString() || '',
          readingScore: data.readingScore?.toString() || '',
          readingBandScore: data.readingBandScore?.toString() || '',
          writingTask1Score: data.writingTask1Score?.toString() || '',
          writingTask2Score: data.writingTask2Score?.toString() || '',
          writingBandScore: data.writingBandScore?.toString() || '',
          speakingFluencyScore: data.speakingFluencyScore?.toString() || '',
          speakingLexicalScore: data.speakingLexicalScore?.toString() || '',
          speakingGrammarScore: data.speakingGrammarScore?.toString() || '',
          speakingPronunciationScore: data.speakingPronunciationScore?.toString() || '',
          speakingBandScore: data.speakingBandScore?.toString() || '',
          overallBandScore: data.overallBandScore?.toString() || '',
          status: data.status,
        });
      } else {
        setError('Result not found');
      }
    } catch (error) {
      console.error('Error fetching result:', error);
      setError('Failed to load result');
    } finally {
      setIsLoading(false);
    }
  }, [resultId]);

  useEffect(() => {
    if (resultId) {
      fetchResult();
    }
  }, [resultId, fetchResult]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const calculateOverallBand = useCallback(() => {
    const { listeningBandScore, readingBandScore, writingBandScore, speakingBandScore } = formData;

    if (listeningBandScore && readingBandScore && writingBandScore && speakingBandScore) {
      const average = (
        parseFloat(listeningBandScore) +
        parseFloat(readingBandScore) +
        parseFloat(writingBandScore) +
        parseFloat(speakingBandScore)
      ) / 4;

      // Round to nearest 0.5
      const rounded = Math.round(average * 2) / 2;
      setFormData(prev => ({ ...prev, overallBandScore: rounded.toString() }));
    }
  }, [formData]);

  useEffect(() => {
    calculateOverallBand();
  }, [calculateOverallBand]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setError('');

    try {
      const response = await fetch(`/api/admin/results/${resultId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push(`/admin/results/${resultId}`);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to update results');
      }
    } catch {
      setError('An error occurred. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error && !result) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Result</h3>
        <p className="text-gray-600">{error}</p>
        <Link
          href="/admin/results"
          className="mt-4 inline-flex items-center text-blue-600 hover:text-blue-700"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Results
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href={`/admin/results/${resultId}`}
            className="mr-4 p-2 text-gray-400 hover:text-gray-600"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Test Results</h1>
            <p className="text-gray-600">Update IELTS test scores</p>
          </div>
        </div>
      </div>

      {/* Candidate Info */}
      {result && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <User className="h-5 w-5 text-blue-600 mr-2" />
            <div>
              <h3 className="font-medium text-blue-900">{result.candidate.fullName}</h3>
              <p className="text-sm text-blue-700">
                {result.candidate.passportNumber} • Test Date: {new Date(result.candidate.testDate).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Edit Form */}
      <div className="bg-white shadow rounded-lg">
        <form onSubmit={handleSubmit} className="p-6 space-y-8">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
              {error}
            </div>
          )}

          {/* Status Selection */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
              Result Status
            </label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleInputChange}
              className="w-full max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
              <option value="verified">Verified</option>
            </select>
          </div>

          {/* Listening Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Headphones className="h-5 w-5 mr-2 text-blue-600" />
              Listening
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="listeningScore" className="block text-sm font-medium text-gray-700 mb-2">
                  Raw Score (0-40)
                </label>
                <input
                  type="number"
                  id="listeningScore"
                  name="listeningScore"
                  value={formData.listeningScore}
                  onChange={handleInputChange}
                  min="0"
                  max="40"
                  step="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label htmlFor="listeningBandScore" className="block text-sm font-medium text-gray-700 mb-2">
                  Band Score (1-9)
                </label>
                <input
                  type="number"
                  id="listeningBandScore"
                  name="listeningBandScore"
                  value={formData.listeningBandScore}
                  onChange={handleInputChange}
                  min="1"
                  max="9"
                  step="0.5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Reading Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <BookOpen className="h-5 w-5 mr-2 text-green-600" />
              Reading
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="readingScore" className="block text-sm font-medium text-gray-700 mb-2">
                  Raw Score (0-40)
                </label>
                <input
                  type="number"
                  id="readingScore"
                  name="readingScore"
                  value={formData.readingScore}
                  onChange={handleInputChange}
                  min="0"
                  max="40"
                  step="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label htmlFor="readingBandScore" className="block text-sm font-medium text-gray-700 mb-2">
                  Band Score (1-9)
                </label>
                <input
                  type="number"
                  id="readingBandScore"
                  name="readingBandScore"
                  value={formData.readingBandScore}
                  onChange={handleInputChange}
                  min="1"
                  max="9"
                  step="0.5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Writing Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <PenTool className="h-5 w-5 mr-2 text-purple-600" />
              Writing
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label htmlFor="writingTask1Score" className="block text-sm font-medium text-gray-700 mb-2">
                  Task 1 Score (1-9)
                </label>
                <input
                  type="number"
                  id="writingTask1Score"
                  name="writingTask1Score"
                  value={formData.writingTask1Score}
                  onChange={handleInputChange}
                  min="1"
                  max="9"
                  step="0.5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label htmlFor="writingTask2Score" className="block text-sm font-medium text-gray-700 mb-2">
                  Task 2 Score (1-9)
                </label>
                <input
                  type="number"
                  id="writingTask2Score"
                  name="writingTask2Score"
                  value={formData.writingTask2Score}
                  onChange={handleInputChange}
                  min="1"
                  max="9"
                  step="0.5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label htmlFor="writingBandScore" className="block text-sm font-medium text-gray-700 mb-2">
                  Overall Band Score (1-9)
                </label>
                <input
                  type="number"
                  id="writingBandScore"
                  name="writingBandScore"
                  value={formData.writingBandScore}
                  onChange={handleInputChange}
                  min="1"
                  max="9"
                  step="0.5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Speaking Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Mic className="h-5 w-5 mr-2 text-red-600" />
              Speaking
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              <div>
                <label htmlFor="speakingFluencyScore" className="block text-sm font-medium text-gray-700 mb-2">
                  Fluency & Coherence
                </label>
                <input
                  type="number"
                  id="speakingFluencyScore"
                  name="speakingFluencyScore"
                  value={formData.speakingFluencyScore}
                  onChange={handleInputChange}
                  min="1"
                  max="9"
                  step="0.5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label htmlFor="speakingLexicalScore" className="block text-sm font-medium text-gray-700 mb-2">
                  Lexical Resource
                </label>
                <input
                  type="number"
                  id="speakingLexicalScore"
                  name="speakingLexicalScore"
                  value={formData.speakingLexicalScore}
                  onChange={handleInputChange}
                  min="1"
                  max="9"
                  step="0.5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label htmlFor="speakingGrammarScore" className="block text-sm font-medium text-gray-700 mb-2">
                  Grammar & Accuracy
                </label>
                <input
                  type="number"
                  id="speakingGrammarScore"
                  name="speakingGrammarScore"
                  value={formData.speakingGrammarScore}
                  onChange={handleInputChange}
                  min="1"
                  max="9"
                  step="0.5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label htmlFor="speakingPronunciationScore" className="block text-sm font-medium text-gray-700 mb-2">
                  Pronunciation
                </label>
                <input
                  type="number"
                  id="speakingPronunciationScore"
                  name="speakingPronunciationScore"
                  value={formData.speakingPronunciationScore}
                  onChange={handleInputChange}
                  min="1"
                  max="9"
                  step="0.5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label htmlFor="speakingBandScore" className="block text-sm font-medium text-gray-700 mb-2">
                  Overall Band Score
                </label>
                <input
                  type="number"
                  id="speakingBandScore"
                  name="speakingBandScore"
                  value={formData.speakingBandScore}
                  onChange={handleInputChange}
                  min="1"
                  max="9"
                  step="0.5"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Overall Score */}
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <Calculator className="h-5 w-5 mr-2 text-indigo-600" />
              Overall Band Score
            </h3>
            <div className="max-w-xs">
              <label htmlFor="overallBandScore" className="block text-sm font-medium text-gray-700 mb-2">
                Calculated Overall Score
              </label>
              <input
                type="number"
                id="overallBandScore"
                name="overallBandScore"
                value={formData.overallBandScore}
                onChange={handleInputChange}
                min="1"
                max="9"
                step="0.5"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white"
                readOnly
              />
              <p className="mt-1 text-xs text-gray-500">
                Automatically calculated from individual band scores
              </p>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <Link
              href={`/admin/results/${resultId}`}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
