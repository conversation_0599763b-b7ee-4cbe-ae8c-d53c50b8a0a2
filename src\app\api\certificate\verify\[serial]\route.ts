import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { testResults, candidates, testRegistrations } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ serial: string }> }
) {
  try {
    const { serial: certificateSerial } = await params;

    // Validate serial format
    if (!certificateSerial || certificateSerial.length < 5) {
      return NextResponse.json(
        { error: 'Invalid certificate serial format' },
        { status: 400 }
      );
    }

    // Get test result with candidate info using certificate serial
    // Updated to use the new schema with test_registrations table
    const result = await db
      .select({
        id: testResults.id,
        certificateSerial: testResults.certificateSerial,
        certificateGenerated: testResults.certificateGenerated,
        testDate: testResults.testDate,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        createdAt: testResults.createdAt,
        candidate: {
          fullName: candidates.fullName,
          nationality: candidates.nationality,
          testDate: testRegistrations.testDate,
          testCenter: testRegistrations.testCenter,
        },
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(eq(testResults.certificateSerial, certificateSerial))
      .limit(1);

    if (!result.length) {
      return NextResponse.json(
        {
          valid: false,
          error: 'Certificate not found',
          message: 'No certificate found with this serial number'
        },
        { status: 404 }
      );
    }

    const testResult = result[0];

    // Check if certificate was actually generated
    if (!testResult.certificateGenerated) {
      return NextResponse.json(
        {
          valid: false,
          error: 'Certificate not generated',
          message: 'Certificate has not been generated for this result'
        },
        { status: 404 }
      );
    }

    // Only verify completed or verified results
    if (testResult.status === 'pending') {
      return NextResponse.json(
        {
          valid: false,
          error: 'Result pending',
          message: 'Test result is still pending verification'
        },
        { status: 403 }
      );
    }

    // Return verification details
    return NextResponse.json({
      valid: true,
      certificate: {
        serial: testResult.certificateSerial,
        resultId: testResult.id,
        candidateName: testResult.candidate.fullName,
        nationality: testResult.candidate.nationality,
        testDate: testResult.candidate.testDate,
        testCenter: testResult.candidate.testCenter,
        overallBandScore: testResult.overallBandScore,
        status: testResult.status,
        issueDate: testResult.createdAt,
      },
      verification: {
        verified: true,
        verifiedAt: new Date().toISOString(),
        message: 'Certificate is valid and authentic'
      }
    });

  } catch (error) {
    console.error('Error verifying certificate:', error);
    return NextResponse.json(
      {
        valid: false,
        error: 'Verification failed',
        message: 'An error occurred during certificate verification'
      },
      { status: 500 }
    );
  }
}
