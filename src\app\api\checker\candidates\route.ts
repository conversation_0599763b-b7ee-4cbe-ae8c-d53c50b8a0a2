import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testRegistrations, testResults } from '@/lib/db/schema';
import { ilike, or, desc, count, eq, and } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const includeResults = searchParams.get('includeResults') === 'true';
    const testDate = searchParams.get('testDate');

    const offset = (page - 1) * limit;

    // Build search conditions
    const conditions = [];

    if (search) {
      conditions.push(
        or(
          ilike(candidates.fullName, `%${search}%`),
          ilike(candidates.email, `%${search}%`),
          ilike(candidates.passportNumber, `%${search}%`),
          ilike(testRegistrations.candidateNumber, `%${search}%`)
        )
      );
    }

    // Filter by test date if provided
    if (testDate) {
      const targetDate = new Date(testDate);
      // Filter for test registrations with the exact test date
      conditions.push(eq(testRegistrations.testDate, targetDate));
    }

    const searchConditions = conditions.length > 0 ? and(...conditions) : undefined;

    if (includeResults) {
      // Get test registrations with candidates and their test results
      const candidatesWithResults = await db
        .select({
          id: candidates.id,
          candidateNumber: testRegistrations.candidateNumber,
          fullName: candidates.fullName,
          email: candidates.email,
          phoneNumber: candidates.phoneNumber,
          dateOfBirth: candidates.dateOfBirth,
          nationality: candidates.nationality,
          passportNumber: candidates.passportNumber,
          testDate: testRegistrations.testDate,
          testCenter: testRegistrations.testCenter,
          photoUrl: candidates.photoUrl,
          createdAt: testRegistrations.createdAt,
          registrationId: testRegistrations.id,
          // Test results
          resultId: testResults.id,
          listeningBandScore: testResults.listeningBandScore,
          readingBandScore: testResults.readingBandScore,
          writingTask1Score: testResults.writingTask1Score,
          writingTask2Score: testResults.writingTask2Score,
          writingBandScore: testResults.writingBandScore,
          speakingBandScore: testResults.speakingBandScore,
          overallBandScore: testResults.overallBandScore,
          status: testResults.status,
        })
        .from(testRegistrations)
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .leftJoin(testResults, eq(testResults.testRegistrationId, testRegistrations.id))
        .where(searchConditions)
        .orderBy(desc(testRegistrations.createdAt))
        .limit(limit)
        .offset(offset);

      // Transform the data to include hasResult flag and nested result object
      const transformedCandidates = candidatesWithResults.map(candidate => ({
        id: candidate.id,
        candidateNumber: candidate.candidateNumber,
        fullName: candidate.fullName,
        email: candidate.email,
        phoneNumber: candidate.phoneNumber,
        dateOfBirth: candidate.dateOfBirth,
        nationality: candidate.nationality,
        passportNumber: candidate.passportNumber,
        testDate: candidate.testDate,
        testCenter: candidate.testCenter,
        photoUrl: candidate.photoUrl,
        createdAt: candidate.createdAt,
        registrationId: candidate.registrationId,
        hasResult: !!candidate.resultId,
        result: candidate.resultId ? {
          id: candidate.resultId,
          listeningBandScore: candidate.listeningBandScore,
          readingBandScore: candidate.readingBandScore,
          writingTask1Score: candidate.writingTask1Score,
          writingTask2Score: candidate.writingTask2Score,
          writingBandScore: candidate.writingBandScore,
          speakingBandScore: candidate.speakingBandScore,
          overallBandScore: candidate.overallBandScore,
          status: candidate.status,
        } : null,
      }));

      // Get total count
      const totalResult = await db
        .select({ count: count() })
        .from(testRegistrations)
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .where(searchConditions);

      const total = totalResult[0]?.count || 0;

      return NextResponse.json({
        candidates: transformedCandidates,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      });
    } else {
      // Get total count
      const totalResult = await db
        .select({ count: count() })
        .from(testRegistrations)
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .where(searchConditions);

      const total = totalResult[0]?.count || 0;

      // Get test registrations with candidate data
      const candidatesList = await db
        .select({
          id: candidates.id,
          candidateNumber: testRegistrations.candidateNumber,
          fullName: candidates.fullName,
          email: candidates.email,
          phoneNumber: candidates.phoneNumber,
          dateOfBirth: candidates.dateOfBirth,
          nationality: candidates.nationality,
          passportNumber: candidates.passportNumber,
          testDate: testRegistrations.testDate,
          testCenter: testRegistrations.testCenter,
          photoUrl: candidates.photoUrl,
          createdAt: testRegistrations.createdAt,
          registrationId: testRegistrations.id,
        })
        .from(testRegistrations)
        .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
        .where(searchConditions)
        .orderBy(desc(testRegistrations.createdAt))
        .limit(limit)
        .offset(offset);

      return NextResponse.json({
        candidates: candidatesList,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      });
    }
  } catch (error) {
    console.error('Error fetching candidates:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
