import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { testResults, candidates, testRegistrations } from '@/lib/db/schema';
import { ilike, eq, or } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { query } = await request.json();

    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    // Search for test results by candidate name or passport number
    const results = await db
      .select({
        id: testResults.id,
        testRegistrationId: testResults.testRegistrationId,
        listeningBandScore: testResults.listeningBandScore,
        readingBandScore: testResults.readingBandScore,
        writingBandScore: testResults.writingBandScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        createdAt: testResults.createdAt,
        candidate: {
          fullName: candidates.fullName,
          passportNumber: candidates.passportNumber,
        },
        testRegistration: {
          testDate: testRegistrations.testDate,
          testCenter: testRegistrations.testCenter,
          candidateNumber: testRegistrations.candidateNumber,
        },
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(
        or(
          ilike(candidates.fullName, `%${query}%`),
          ilike(candidates.passportNumber, `%${query}%`)
        )
      )
      .orderBy(candidates.fullName)
      .limit(10);

    return NextResponse.json(results);
  } catch (error) {
    console.error('Error searching test results:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
