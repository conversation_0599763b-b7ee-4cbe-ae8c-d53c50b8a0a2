'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Shield, Search, ArrowLeft, FileText } from 'lucide-react';

export default function VerifyPage() {
  const [serial, setSerial] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!serial.trim()) return;

    setIsLoading(true);

    // Navigate to the verification result page
    router.push(`/verify/${encodeURIComponent(serial.trim())}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center text-blue-600 hover:text-blue-700 mr-4">
                <ArrowLeft className="h-5 w-5 mr-1" />
                Home
              </Link>
              <Shield className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Certificate Verification</h1>
                <p className="text-gray-600">Verify IELTS Certificate Authenticity</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Verification Form */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <div className="text-center mb-8">
            <Shield className="h-16 w-16 text-blue-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Verify Your IELTS Certificate</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Enter your certificate serial number to verify its authenticity and view the associated test results.
              The serial number can be found on your official IELTS certificate.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="max-w-md mx-auto">
            <div className="mb-6">
              <label htmlFor="serial" className="block text-sm font-medium text-gray-700 mb-2">
                Certificate Serial Number
              </label>
              <input
                type="text"
                id="serial"
                value={serial}
                onChange={(e) => setSerial(e.target.value)}
                placeholder="IELTS-2024-123456"
                className="w-full px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-center font-mono text-lg"
                required
              />
              <p className="mt-2 text-sm text-gray-500">
                Format: IELTS-YYYY-NNNNNN (e.g., IELTS-2024-123456)
              </p>
            </div>

            <button
              type="submit"
              disabled={isLoading || !serial.trim()}
              className="w-full flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Verifying...
                </>
              ) : (
                <>
                  <Search className="h-5 w-5 mr-2" />
                  Verify Certificate
                </>
              )}
            </button>
          </form>
        </div>

        {/* Information Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <FileText className="h-6 w-6 text-green-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">What You Can Verify</h3>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Certificate authenticity and validity</li>
              <li>• Candidate information and test details</li>
              <li>• Test scores and band scores</li>
              <li>• Test date and center information</li>
              <li>• Certificate issue date</li>
            </ul>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <Shield className="h-6 w-6 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Security Features</h3>
            </div>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Unique serial number verification</li>
              <li>• Real-time database validation</li>
              <li>• Secure certificate generation</li>
              <li>• Tamper-proof verification system</li>
              <li>• Official IELTS authentication</li>
            </ul>
          </div>
        </div>

        {/* Help Section */}
        <div className="mt-12 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 mb-4">Need Help?</h3>
          <div className="text-sm text-yellow-700 space-y-2">
            <p>If you&apos;re having trouble verifying your certificate:</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Double-check the serial number format (IELTS-YYYY-NNNNNN)</li>
              <li>Ensure you&apos;re entering the complete serial number</li>
              <li>Contact your test center if the certificate was recently issued</li>
              <li>Reach out to IELTS support for additional assistance</li>
            </ul>
          </div>
        </div>

        {/* Footer Links */}
        <div className="mt-12 text-center">
          <div className="flex justify-center space-x-6 text-sm">
            <Link
              href="/search"
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Search Test Results
            </Link>
            <Link
              href="/"
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}
