'use client';

import { useState, useRef } from 'react';
import { Upload, X, Image as ImageIcon, FileText, Loader2 } from 'lucide-react';
import Image from 'next/image';

interface FileUploadProps {
  type: 'photo' | 'document';
  onUpload: (url: string) => void;
  onRemove?: () => void;
  currentFile?: string;
  accept?: string;
  maxSize?: number; // in MB
  className?: string;
  disabled?: boolean;
}

export default function FileUpload({
  type,
  onUpload,
  onRemove,
  currentFile,
  accept,
  maxSize = 5,
  className = '',
  disabled = false,
}: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const defaultAccept = type === 'photo'
    ? 'image/jpeg,image/jpg,image/png,image/webp'
    : 'application/pdf,image/jpeg,image/jpg,image/png';

  const handleFileSelect = async (file: File) => {
    if (disabled) return;

    setError('');
    setIsUploading(true);

    try {
      // Validate file size
      if (file.size > maxSize * 1024 * 1024) {
        throw new Error(`File size must be less than ${maxSize}MB`);
      }

      // Create form data
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      // Upload file
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const data = await response.json();
      onUpload(data.url);
    } catch (error) {
      console.error('Upload error:', error);
      setError(error instanceof Error ? error.message : 'Upload failed');
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);

    if (disabled) return;

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setDragActive(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleRemove = async () => {
    if (!currentFile || disabled) return;

    try {
      // Delete file from server
      await fetch(`/api/upload?url=${encodeURIComponent(currentFile)}`, {
        method: 'DELETE',
      });

      onRemove?.();
    } catch (error) {
      console.error('Remove error:', error);
    }
  };

  const openFileDialog = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  if (currentFile) {
    return (
      <div className={`relative ${className}`}>
        <div className="border-2 border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {type === 'photo' ? (
                <div className="relative">
                  <Image
                    src={currentFile}
                    alt="Uploaded file"
                    className="h-16 w-16 object-cover rounded-lg"
                    width={64}
                    height={64}
                  />
                </div>
              ) : (
                <div className="h-16 w-16 bg-gray-100 rounded-lg flex items-center justify-center">
                  <FileText className="h-8 w-8 text-gray-400" />
                </div>
              )}
              <div>
                <p className="text-sm font-medium text-gray-900">File uploaded</p>
                <p className="text-xs text-gray-500">Click remove to change file</p>
              </div>
            </div>
            {!disabled && (
              <button
                onClick={handleRemove}
                className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                title="Remove file"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept || defaultAccept}
        onChange={handleFileChange}
        className="hidden"
        disabled={disabled}
      />

      <div
        onClick={openFileDialog}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all
          ${dragActive
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          ${error ? 'border-red-300 bg-red-50' : ''}
        `}
      >
        {isUploading ? (
          <div className="flex flex-col items-center">
            <Loader2 className="h-12 w-12 text-blue-500 animate-spin mb-4" />
            <p className="text-sm text-gray-600">Uploading...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            {type === 'photo' ? (
              <ImageIcon className="h-12 w-12 text-gray-400 mb-4" />
            ) : (
              <Upload className="h-12 w-12 text-gray-400 mb-4" />
            )}
            <p className="text-sm font-medium text-gray-900 mb-2">
              {type === 'photo' ? 'Upload Photo' : 'Upload Document'}
            </p>
            <p className="text-xs text-gray-500 mb-2">
              Drag and drop or click to select
            </p>
            <p className="text-xs text-gray-400">
              {type === 'photo'
                ? 'PNG, JPG, WEBP up to 5MB'
                : 'PDF, PNG, JPG up to 5MB'
              }
            </p>
          </div>
        )}
      </div>

      {error && (
        <p className="mt-2 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}
