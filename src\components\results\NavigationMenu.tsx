'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  TrendingUp,
  MessageSquare,
  Award,
  BarChart3,
  ArrowLeft,
  CheckCircle,
  Clock
} from 'lucide-react';

interface NavigationMenuProps {
  resultId: string;
  className?: string;
  completionStatus?: {
    overview: boolean;
    progress: boolean;
    feedback: boolean;
    certificate: boolean;
  };
}

export default function NavigationMenu({
  resultId,
  className = '',
  completionStatus = {
    overview: true,
    progress: true,
    feedback: true,
    certificate: false
  }
}: NavigationMenuProps) {
  const pathname = usePathname();

  const menuItems = [
    {
      href: `/results/${resultId}`,
      label: 'Overview',
      icon: LayoutDashboard,
      description: 'Summary & Main Results',
      color: 'blue',
      completed: completionStatus.overview
    },
    {
      href: `/results/${resultId}/progress`,
      label: 'Progress',
      icon: TrendingUp,
      description: 'Module Progress Tracking',
      color: 'emerald',
      completed: completionStatus.progress
    },
    {
      href: `/results/${resultId}/feedback`,
      label: 'Feedback',
      icon: MessageSquare,
      description: 'Detailed Analysis & Tips',
      color: 'amber',
      completed: completionStatus.feedback
    },
    {
      href: `/results/${resultId}/certificate`,
      label: 'Certificate',
      icon: Award,
      description: 'Official Certificate',
      color: 'purple',
      completed: completionStatus.certificate
    }
  ];

  const isActive = (href: string) => {
    if (href === `/results/${resultId}`) {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const getStatusIcon = (completed: boolean, isActive: boolean) => {
    if (completed) {
      return <CheckCircle className={`h-4 w-4 ${isActive ? 'text-primary-600' : 'text-success-500'}`} />;
    }
    return <Clock className={`h-4 w-4 ${isActive ? 'text-primary-600' : 'text-muted-foreground'}`} />;
  };

  const getColorClasses = (color: string, active: boolean) => {
    if (active) {
      return {
        container: 'bg-primary-50 border-l-4 border-primary-500 shadow-sm',
        icon: 'text-primary-600',
        title: 'text-primary-900',
        description: 'text-primary-700'
      };
    }

    const colorMap = {
      blue: {
        container: 'hover:bg-blue-50 hover:border-blue-200',
        icon: 'text-blue-500 group-hover:text-blue-600',
        title: 'text-gray-900 group-hover:text-blue-900',
        description: 'text-gray-600 group-hover:text-blue-700'
      },
      emerald: {
        container: 'hover:bg-emerald-50 hover:border-emerald-200',
        icon: 'text-emerald-500 group-hover:text-emerald-600',
        title: 'text-gray-900 group-hover:text-emerald-900',
        description: 'text-gray-600 group-hover:text-emerald-700'
      },
      amber: {
        container: 'hover:bg-amber-50 hover:border-amber-200',
        icon: 'text-amber-500 group-hover:text-amber-600',
        title: 'text-gray-900 group-hover:text-amber-900',
        description: 'text-gray-600 group-hover:text-amber-700'
      },
      purple: {
        container: 'hover:bg-purple-50 hover:border-purple-200',
        icon: 'text-purple-500 group-hover:text-purple-600',
        title: 'text-gray-900 group-hover:text-purple-900',
        description: 'text-gray-600 group-hover:text-purple-700'
      }
    };

    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className={`card-elevated animate-fade-in ${className}`}>
      <div className="p-6">
        <div className="flex items-center mb-6">
          <div className="p-2 bg-primary-100 rounded-lg mr-3">
            <BarChart3 className="h-5 w-5 text-primary-600" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Result Sections</h2>
            <p className="text-sm text-muted-foreground">Navigate through your results</p>
          </div>
        </div>

        <nav className="space-y-2">
          {menuItems.map((item, index) => {
            const Icon = item.icon;
            const active = isActive(item.href);
            const colors = getColorClasses(item.color, active);

            return (
              <Link
                key={item.href}
                href={item.href}
                className={`group block w-full p-4 rounded-xl border border-transparent transition-all duration-200 focus-ring animate-slide-up ${colors.container}`}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center flex-1">
                    <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-white shadow-sm mr-3">
                      <Icon className={`h-5 w-5 transition-colors duration-200 ${colors.icon}`} />
                    </div>
                    <div className="flex-1">
                      <div className={`font-medium transition-colors duration-200 ${colors.title}`}>
                        {item.label}
                      </div>
                      <div className={`text-sm transition-colors duration-200 ${colors.description}`}>
                        {item.description}
                      </div>
                    </div>
                  </div>
                  <div className="ml-3">
                    {getStatusIcon(item.completed, active)}
                  </div>
                </div>
              </Link>
            );
          })}
        </nav>

        <div className="mt-6 pt-6 border-t border-border">
          <Link
            href="/search"
            className="flex items-center text-sm text-muted-foreground hover:text-foreground transition-colors duration-200 focus-ring rounded-md p-2 -m-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Search
          </Link>
        </div>
      </div>
    </div>
  );
}
