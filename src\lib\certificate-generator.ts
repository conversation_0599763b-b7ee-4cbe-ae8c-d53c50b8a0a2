import jsPDF from 'jspdf';
import { TestResult, Candidate } from './db/schema';
import { formatDate, formatBandScore, getBandScoreDescription } from './utils';

interface RegistrationInfo {
  candidateNumber: string;
  testDate: Date;
  testCenter: string;
}

export async function generateCertificate(
  testResult: TestResult,
  candidate: Candidate,
  registration?: RegistrationInfo
): Promise<string> {
  const pdf = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });

  // Set up the certificate design
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();

  // ===== FRONT PAGE =====
  createFrontPage(pdf, testResult, candidate, registration, pageWidth, pageHeight);

  // ===== BACK PAGE =====
  pdf.addPage();
  createBackPage(pdf, testResult, candidate, pageWidth, pageHeight);

  // Convert to base64 string
  const pdfBase64 = pdf.output('datauristring');
  return pdfBase64;
}

function createFrontPage(pdf: jsPDF, testResult: TestResult, candidate: Candidate, registration: RegistrationInfo | undefined, pageWidth: number, pageHeight: number) {
  // White background
  pdf.setFillColor(255, 255, 255);
  pdf.rect(0, 0, pageWidth, pageHeight, 'F');

  // Main border
  pdf.setDrawColor(0, 0, 0);
  pdf.setLineWidth(2);
  pdf.rect(8, 8, pageWidth - 16, pageHeight - 16);

  // Inner border
  pdf.setLineWidth(0.5);
  pdf.rect(12, 12, pageWidth - 24, pageHeight - 24);

  // Header section with IELTS branding
  pdf.setFillColor(0, 51, 102); // Dark blue background
  pdf.rect(12, 12, pageWidth - 24, 35, 'F');

  // Main title - UPDATED (no logo)
  pdf.setTextColor(255, 255, 255);
  pdf.setFontSize(20);
  pdf.setFont('helvetica', 'bold');
  pdf.text('IELTS MOCK Certificate', pageWidth / 2, 30, { align: 'center' });

  // Remove the old subtitle and validity text - they are no longer included

  // Candidate details section
  pdf.setTextColor(0, 0, 0);
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('CANDIDATE DETAILS', 15, 65);

  // Create a box for candidate details
  pdf.setDrawColor(0, 0, 0);
  pdf.setLineWidth(0.5);
  pdf.rect(15, 70, pageWidth - 30, 60);

  // Candidate photo (right side) - ENHANCED LARGER PHOTO
  const photoX = pageWidth - 55;
  const photoY = 75;
  const photoWidth = 35;
  const photoHeight = 40;

  // Photo border
  pdf.setLineWidth(1);
  pdf.rect(photoX, photoY, photoWidth, photoHeight);

  // Add candidate photo if available - ENHANCED DEBUGGING AND HANDLING
  if (candidate.photoData) {
    try {
      console.log('Photo data available, length:', candidate.photoData.length);

      // Prepare photo data with better handling
      let photoData = candidate.photoData;

      // Handle different photo data formats
      if (photoData.startsWith('data:image/')) {
        // Already has data URL prefix
        console.log('Photo data has data URL prefix');
      } else if (photoData.startsWith('/9j/') || photoData.match(/^[A-Za-z0-9+/=]+$/)) {
        // Looks like base64 data
        photoData = `data:image/jpeg;base64,${photoData}`;
        console.log('Added data URL prefix to base64 data');
      } else {
        throw new Error('Unrecognized photo data format');
      }

      // Add the photo with proper sizing - ENLARGED PHOTO
      pdf.addImage(photoData, 'JPEG', photoX + 1, photoY + 1, photoWidth - 2, photoHeight - 2);
      console.log('Photo successfully added to certificate');
    } catch (error) {
      console.error('Could not add photo to certificate:', error);
      console.log('Photo data preview:', candidate.photoData?.substring(0, 100));

      // Enhanced fallback placeholder
      pdf.setFillColor(245, 245, 245);
      pdf.rect(photoX + 1, photoY + 1, photoWidth - 2, photoHeight - 2, 'F');
      pdf.setFontSize(8);
      pdf.setTextColor(128, 128, 128);
      pdf.text('Candidate', photoX + photoWidth/2, photoY + photoHeight/2 - 2, { align: 'center' });
      pdf.text('Photo', photoX + photoWidth/2, photoY + photoHeight/2 + 2, { align: 'center' });
    }
  } else {
    console.log('No photo data available for candidate');
    // Enhanced placeholder if no photo
    pdf.setFillColor(245, 245, 245);
    pdf.rect(photoX + 1, photoY + 1, photoWidth - 2, photoHeight - 2, 'F');
    pdf.setFontSize(8);
    pdf.setTextColor(128, 128, 128);
    pdf.text('Candidate', photoX + photoWidth/2, photoY + photoHeight/2 - 2, { align: 'center' });
    pdf.text('Photo', photoX + photoWidth/2, photoY + photoHeight/2 + 2, { align: 'center' });
  }

  // Candidate information (left side) - IMPROVED LAYOUT
  pdf.setTextColor(0, 0, 0);
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  const leftCol = 18;
  const rightCol = 85; // Reduced to prevent overflow

  pdf.text('Family Name:', leftCol, 85);
  const familyName = candidate.fullName.split(' ').slice(-1)[0].toUpperCase();
  pdf.text(familyName.length > 20 ? familyName.substring(0, 20) + '...' : familyName, rightCol, 85);

  pdf.text('Given Name(s):', leftCol, 92);
  const givenNames = candidate.fullName.split(' ').slice(0, -1).join(' ').toUpperCase();
  pdf.text(givenNames.length > 20 ? givenNames.substring(0, 20) + '...' : givenNames, rightCol, 92);

  pdf.text('Candidate Number:', leftCol, 99);
  pdf.text(registration?.candidateNumber || 'N/A', rightCol, 99);

  pdf.text('Date of Birth:', leftCol, 106);
  pdf.text(formatDate(new Date(candidate.dateOfBirth)), rightCol, 106);

  pdf.text('Identification Type:', leftCol, 113);
  pdf.text('Passport', rightCol, 113);

  pdf.text('Identification Number:', leftCol, 120);
  const passportNum = candidate.passportNumber;
  pdf.text(passportNum.length > 15 ? passportNum.substring(0, 15) + '...' : passportNum, rightCol, 120);

  pdf.text('Country/Region of Origin:', leftCol, 127);
  const nationality = candidate.nationality;
  pdf.text(nationality.length > 15 ? nationality.substring(0, 15) + '...' : nationality, rightCol, 127);

  // Test details section
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('TEST DETAILS', 15, 145);

  // Test details box
  pdf.setLineWidth(0.5);
  pdf.rect(15, 150, pageWidth - 30, 25);

  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  pdf.text('Test Date:', 18, 160);
  pdf.text(formatDate(new Date(registration?.testDate || new Date())), 85, 160);

  pdf.text('Test Centre Number:', 18, 167);
  pdf.text('SAM001', 85, 167); // Samarkand test center code

  pdf.text('Test Centre Name:', 18, 174);
  const testCenter = registration?.testCenter || 'Innovative Centre - Samarkand';
  pdf.text(testCenter.length > 25 ? testCenter.substring(0, 25) + '...' : testCenter, 85, 174);

  // Test Results section - MOVED HIGHER
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('TEST RESULTS', 15, 180);

  // Overall Band Score section - MADE MORE COMPACT
  const overallY = 185;
  pdf.setFillColor(0, 51, 102);
  pdf.rect(15, overallY, pageWidth - 30, 20, 'F'); // Reduced height from 25 to 20

  pdf.setTextColor(255, 255, 255);
  pdf.setFontSize(18); // Increased font size for prominence
  pdf.setFont('helvetica', 'bold');

  // More compact layout - all on one line with better spacing
  const overallScore = formatBandScore(Number(testResult.overallBandScore) || 0);
  const cefrLevel = getCEFRLevel(Number(testResult.overallBandScore) || 0);
  const description = getBandScoreDescription(Number(testResult.overallBandScore) || 0);

  pdf.text(`OVERALL BAND SCORE ${overallScore} ${cefrLevel} ${description}`, 20, overallY + 13);

  // Results table - MOVED HIGHER
  const tableStartY = 215; // Moved up from 230
  pdf.setLineWidth(0.5);
  pdf.rect(15, tableStartY, pageWidth - 30, 65);

  // Table headers
  pdf.setFillColor(240, 240, 240);
  pdf.rect(15, tableStartY, pageWidth - 30, 15, 'F');

  pdf.setTextColor(0, 0, 0);
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Skill', 20, tableStartY + 10);
  pdf.text('Band Score', 70, tableStartY + 10);
  pdf.text('CEFR Level', 110, tableStartY + 10);
  pdf.text('Description', 140, tableStartY + 10);

  // Horizontal lines for table structure - IMPROVED SPACING
  pdf.line(15, tableStartY + 15, pageWidth - 15, tableStartY + 15);
  pdf.line(65, tableStartY, 65, tableStartY + 65); // Skill column (reduced width)
  pdf.line(105, tableStartY, 105, tableStartY + 65); // Band Score column (reduced width)
  pdf.line(135, tableStartY, 135, tableStartY + 65); // CEFR column (reduced width)

  // Test results data
  const skills = [
    {
      name: 'Listening',
      band: formatBandScore(Number(testResult.listeningBandScore) || 0),
      cefr: getCEFRLevel(Number(testResult.listeningBandScore) || 0),
      description: getBandScoreDescription(Number(testResult.listeningBandScore) || 0)
    },
    {
      name: 'Reading',
      band: formatBandScore(Number(testResult.readingBandScore) || 0),
      cefr: getCEFRLevel(Number(testResult.readingBandScore) || 0),
      description: getBandScoreDescription(Number(testResult.readingBandScore) || 0)
    },
    {
      name: 'Writing',
      band: formatBandScore(Number(testResult.writingBandScore) || 0),
      cefr: getCEFRLevel(Number(testResult.writingBandScore) || 0),
      description: getBandScoreDescription(Number(testResult.writingBandScore) || 0)
    },
    {
      name: 'Speaking',
      band: formatBandScore(Number(testResult.speakingBandScore) || 0),
      cefr: getCEFRLevel(Number(testResult.speakingBandScore) || 0),
      description: getBandScoreDescription(Number(testResult.speakingBandScore) || 0)
    }
  ];

  pdf.setFont('helvetica', 'normal');
  skills.forEach((skill, index) => {
    const rowY = tableStartY + 25 + (index * 12);

    // Horizontal line between rows
    if (index > 0) {
      pdf.line(15, rowY - 6, pageWidth - 15, rowY - 6);
    }

    pdf.text(skill.name, 20, rowY);
    pdf.text(skill.band, 75, rowY);
    pdf.text(skill.cefr, 115, rowY);

    // Improved description handling - shorter text to prevent overflow
    const description = skill.description;
    if (description.length > 15) {
      pdf.text(description.substring(0, 15) + '...', 145, rowY);
    } else {
      pdf.text(description, 145, rowY);
    }
  });
}

function createBackPage(pdf: jsPDF, testResult: TestResult, candidate: Candidate, pageWidth: number, pageHeight: number) {
  // White background
  pdf.setFillColor(255, 255, 255);
  pdf.rect(0, 0, pageWidth, pageHeight, 'F');

  // Main border
  pdf.setDrawColor(0, 0, 0);
  pdf.setLineWidth(2);
  pdf.rect(8, 8, pageWidth - 16, pageHeight - 16);

  // Inner border
  pdf.setLineWidth(0.5);
  pdf.rect(12, 12, pageWidth - 24, pageHeight - 24);

  // Header section
  pdf.setFillColor(0, 51, 102);
  pdf.rect(12, 12, pageWidth - 24, 35, 'F');

  pdf.setTextColor(255, 255, 255);
  pdf.setFontSize(18);
  pdf.setFont('helvetica', 'bold');
  pdf.text('IELTS MOCK Certificate', pageWidth / 2, 25, { align: 'center' });

  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'normal');
  pdf.text('Additional Information', pageWidth / 2, 35, { align: 'center' });

  // Authentication section
  pdf.setTextColor(0, 0, 0);
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('AUTHENTICATION', 15, 65);

  pdf.setLineWidth(0.5);
  pdf.rect(15, 70, pageWidth - 30, 40);

  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  pdf.text('Centre Stamp:', 20, 85);
  pdf.text('Authorised Signature:', 20, 100);

  // Certificate serial and verification info
  pdf.text(`Certificate Serial: ${testResult.certificateSerial || 'N/A'}`, 120, 85);
  pdf.text(`Issue Date: ${formatDate(new Date())}`, 120, 100);

  // Band Score Descriptions
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('BAND SCORE DESCRIPTIONS', 15, 130);

  pdf.setLineWidth(0.5);
  pdf.rect(15, 135, pageWidth - 30, 120);

  pdf.setFontSize(9);
  pdf.setFont('helvetica', 'normal');

  const descriptions = [
    'Band 9: Expert User - Has fully operational command of the language',
    'Band 8: Very Good User - Has fully operational command with occasional inaccuracies',
    'Band 7: Good User - Has operational command with occasional inaccuracies',
    'Band 6: Competent User - Has generally effective command despite inaccuracies',
    'Band 5: Modest User - Has partial command with frequent problems',
    'Band 4: Limited User - Basic competence limited to familiar situations',
    'Band 3: Extremely Limited User - Conveys general meaning in familiar situations',
    'Band 2: Intermittent User - No real communication except basic information',
    'Band 1: Non User - No ability to use the language'
  ];

  descriptions.forEach((desc, index) => {
    pdf.text(desc, 20, 145 + (index * 12));
  });

  // Important Notes section removed as requested
}

// Helper function to get CEFR level from band score
function getCEFRLevel(bandScore: number): string {
  if (bandScore >= 8.5) return 'C2';
  if (bandScore >= 7.0) return 'C1';
  if (bandScore >= 5.5) return 'B2';
  if (bandScore >= 4.0) return 'B1';
  if (bandScore >= 3.0) return 'A2';
  return 'A1';
}
