-- Migration to update candidate table constraints
-- Remove unique constraints on email and passport_number
-- Add composite unique constraints for email+test_date and passport_number+test_date

-- Drop existing unique constraints
ALTER TABLE candidates DROP CONSTRAINT IF EXISTS candidates_email_unique;
ALTER TABLE candidates DROP CONSTRAINT IF EXISTS candidates_passport_number_unique;

-- Add composite unique constraints to prevent duplicate registrations for same test date
ALTER TABLE candidates ADD CONSTRAINT candidates_email_test_date_unique 
  UNIQUE (email, test_date);

ALTER TABLE candidates ADD CONSTRAINT candidates_passport_number_test_date_unique 
  UNIQUE (passport_number, test_date);
