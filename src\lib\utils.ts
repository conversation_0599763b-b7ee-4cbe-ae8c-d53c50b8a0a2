import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// IELTS band score calculation utilities
export function calculateOverallBandScore(
  listening: number,
  reading: number,
  writing: number,
  speaking: number
): number {
  const average = (listening + reading + writing + speaking) / 4;
  
  // Round to nearest 0.5
  return Math.round(average * 2) / 2;
}

export function getListeningBandScore(rawScore: number): number {
  // IELTS Listening band score conversion (out of 40)
  const conversionTable: { [key: number]: number } = {
    39: 9.0, 38: 9.0, 37: 8.5, 36: 8.5, 35: 8.0, 34: 8.0, 33: 7.5,
    32: 7.5, 31: 7.0, 30: 7.0, 29: 6.5, 28: 6.5, 27: 6.0, 26: 6.0,
    25: 5.5, 24: 5.5, 23: 5.0, 22: 5.0, 21: 4.5, 20: 4.5, 19: 4.0,
    18: 4.0, 17: 3.5, 16: 3.5, 15: 3.0, 14: 3.0, 13: 2.5, 12: 2.5,
    11: 2.0, 10: 2.0, 9: 1.5, 8: 1.5, 7: 1.0, 6: 1.0, 5: 0.5,
    4: 0.5, 3: 0.0, 2: 0.0, 1: 0.0, 0: 0.0
  };
  
  return conversionTable[Math.floor(rawScore)] || 0.0;
}

export function getReadingBandScore(rawScore: number): number {
  // IELTS Reading band score conversion (out of 40)
  const conversionTable: { [key: number]: number } = {
    39: 9.0, 38: 9.0, 37: 8.5, 36: 8.5, 35: 8.0, 34: 8.0, 33: 7.5,
    32: 7.5, 31: 7.0, 30: 7.0, 29: 6.5, 28: 6.5, 27: 6.0, 26: 6.0,
    25: 5.5, 24: 5.5, 23: 5.0, 22: 5.0, 21: 4.5, 20: 4.5, 19: 4.0,
    18: 4.0, 17: 3.5, 16: 3.5, 15: 3.0, 14: 3.0, 13: 2.5, 12: 2.5,
    11: 2.0, 10: 2.0, 9: 1.5, 8: 1.5, 7: 1.0, 6: 1.0, 5: 0.5,
    4: 0.5, 3: 0.0, 2: 0.0, 1: 0.0, 0: 0.0
  };
  
  return conversionTable[Math.floor(rawScore)] || 0.0;
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
}

export function formatBandScore(score: number): string {
  return score.toFixed(1);
}

export function getBandScoreDescription(score: number): string {
  if (score >= 9.0) return 'Expert User';
  if (score >= 8.0) return 'Very Good User';
  if (score >= 7.0) return 'Good User';
  if (score >= 6.0) return 'Competent User';
  if (score >= 5.0) return 'Modest User';
  if (score >= 4.0) return 'Limited User';
  if (score >= 3.0) return 'Extremely Limited User';
  if (score >= 2.0) return 'Intermittent User';
  if (score >= 1.0) return 'Non User';
  return 'Did not attempt the test';
}
